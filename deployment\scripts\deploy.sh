#!/bin/bash

# 刀刀乐捡漏网部署脚本
# 使用方法: ./deploy.sh [环境] [版本]
# 例如: ./deploy.sh production v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "使用方法: $0 [环境] [版本]"
    log_error "环境: development, staging, production"
    log_error "版本: 可选，默认为latest"
    exit 1
fi

ENVIRONMENT=$1
VERSION=${2:-latest}
PROJECT_NAME="ddlpicker"
BACKUP_DIR="/backup/${PROJECT_NAME}"
DEPLOY_DIR="/opt/${PROJECT_NAME}"

log_info "开始部署 ${PROJECT_NAME} 到 ${ENVIRONMENT} 环境，版本: ${VERSION}"

# 检查环境
case $ENVIRONMENT in
    development|staging|production)
        log_info "环境检查通过: ${ENVIRONMENT}"
        ;;
    *)
        log_error "不支持的环境: ${ENVIRONMENT}"
        exit 1
        ;;
esac

# 检查必要的命令
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

log_info "检查必要的命令..."
check_command docker
check_command docker-compose
check_command git

# 创建必要的目录
log_info "创建必要的目录..."
sudo mkdir -p ${BACKUP_DIR}
sudo mkdir -p ${DEPLOY_DIR}
sudo mkdir -p /var/log/${PROJECT_NAME}

# 备份当前版本
backup_current() {
    if [ -d "${DEPLOY_DIR}" ] && [ "$(ls -A ${DEPLOY_DIR})" ]; then
        log_info "备份当前版本..."
        BACKUP_NAME="${PROJECT_NAME}_$(date +%Y%m%d_%H%M%S)"
        sudo cp -r ${DEPLOY_DIR} ${BACKUP_DIR}/${BACKUP_NAME}
        log_success "备份完成: ${BACKUP_DIR}/${BACKUP_NAME}"
    fi
}

# 拉取最新代码
pull_code() {
    log_info "拉取最新代码..."
    if [ -d "${DEPLOY_DIR}/.git" ]; then
        cd ${DEPLOY_DIR}
        sudo git fetch --all
        sudo git reset --hard origin/main
        sudo git pull origin main
    else
        sudo git clone https://github.com/your-username/${PROJECT_NAME}.git ${DEPLOY_DIR}
        cd ${DEPLOY_DIR}
    fi
    
    if [ "${VERSION}" != "latest" ]; then
        sudo git checkout ${VERSION}
    fi
    
    log_success "代码拉取完成"
}

# 设置环境变量
setup_env() {
    log_info "设置环境变量..."
    
    if [ ! -f "${DEPLOY_DIR}/.env" ]; then
        sudo cp ${DEPLOY_DIR}/.env.example ${DEPLOY_DIR}/.env
        log_warning "请编辑 ${DEPLOY_DIR}/.env 文件，填入正确的配置"
        log_warning "部署将在5秒后继续..."
        sleep 5
    fi
    
    # 根据环境设置不同的配置
    case $ENVIRONMENT in
        production)
            sudo sed -i 's/NODE_ENV=.*/NODE_ENV=production/' ${DEPLOY_DIR}/.env
            ;;
        staging)
            sudo sed -i 's/NODE_ENV=.*/NODE_ENV=staging/' ${DEPLOY_DIR}/.env
            ;;
        development)
            sudo sed -i 's/NODE_ENV=.*/NODE_ENV=development/' ${DEPLOY_DIR}/.env
            ;;
    esac
    
    log_success "环境变量设置完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    cd ${DEPLOY_DIR}
    
    # 构建后端API镜像
    log_info "构建后端API镜像..."
    sudo docker build -t ${PROJECT_NAME}-backend-api:${VERSION} ./backend-api/
    
    # 构建管理端Web镜像
    log_info "构建管理端Web镜像..."
    sudo docker build -t ${PROJECT_NAME}-admin-web:${VERSION} ./admin-web/
    
    log_success "镜像构建完成"
}

# 停止旧服务
stop_services() {
    log_info "停止旧服务..."
    cd ${DEPLOY_DIR}
    
    if sudo docker-compose ps | grep -q "Up"; then
        sudo docker-compose down
        log_success "旧服务已停止"
    else
        log_info "没有运行中的服务"
    fi
}

# 启动新服务
start_services() {
    log_info "启动新服务..."
    cd ${DEPLOY_DIR}
    
    # 启动数据库和Redis
    sudo docker-compose up -d mysql redis
    log_info "等待数据库启动..."
    sleep 30
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    sudo docker-compose run --rm backend-api npm run migrate
    
    # 启动所有服务
    sudo docker-compose up -d
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "进行健康检查..."
    
    # 等待服务启动
    sleep 30
    
    # 检查后端API
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_success "后端API健康检查通过"
    else
        log_error "后端API健康检查失败"
        return 1
    fi
    
    # 检查管理端Web
    if curl -f http://localhost:8080 > /dev/null 2>&1; then
        log_success "管理端Web健康检查通过"
    else
        log_error "管理端Web健康检查失败"
        return 1
    fi
    
    log_success "所有服务健康检查通过"
}

# 清理旧镜像
cleanup() {
    log_info "清理旧镜像..."
    sudo docker image prune -f
    sudo docker system prune -f
    log_success "清理完成"
}

# 主部署流程
main() {
    backup_current
    pull_code
    setup_env
    build_images
    stop_services
    start_services
    
    if health_check; then
        cleanup
        log_success "部署成功完成！"
        log_info "服务访问地址:"
        log_info "  - API: http://localhost:3000"
        log_info "  - 管理后台: http://localhost:8080"
        log_info "  - 主站: http://localhost"
    else
        log_error "健康检查失败，正在回滚..."
        rollback
        exit 1
    fi
}

# 回滚函数
rollback() {
    log_warning "开始回滚..."
    
    # 停止当前服务
    sudo docker-compose down
    
    # 恢复最新备份
    LATEST_BACKUP=$(ls -t ${BACKUP_DIR} | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        sudo rm -rf ${DEPLOY_DIR}
        sudo cp -r ${BACKUP_DIR}/${LATEST_BACKUP} ${DEPLOY_DIR}
        cd ${DEPLOY_DIR}
        sudo docker-compose up -d
        log_success "回滚完成"
    else
        log_error "没有找到备份，无法回滚"
    fi
}

# 捕获中断信号
trap 'log_error "部署被中断"; exit 1' INT TERM

# 执行主流程
main

log_success "部署脚本执行完成！"
