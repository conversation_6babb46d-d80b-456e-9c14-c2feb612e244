module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '用户ID'
    },
    openid: {
      type: DataTypes.STRING(64),
      allowNull: false,
      unique: true,
      comment: '微信openid'
    },
    unionid: {
      type: DataTypes.STRING(64),
      allowNull: true,
      comment: '微信unionid'
    },
    nickname: {
      type: DataTypes.STRING(64),
      allowNull: true,
      comment: '微信昵称'
    },
    avatar_url: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '头像地址'
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      comment: '绑定手机号'
    },
    user_type: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '用户类型：0-C端 1-B端 2-内部员工'
    },
    business_license: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '营业执照OSS地址'
    },
    license_status: {
      type: DataTypes.TINYINT,
      defaultValue: 0,
      comment: '执照审核状态：0-待审 1-通过 2-驳回'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['openid']
      },
      {
        fields: ['user_type']
      },
      {
        fields: ['license_status']
      },
      {
        fields: ['phone']
      }
    ],
    comment: '用户主表'
  })

  // 实例方法
  User.prototype.toJSON = function() {
    const values = { ...this.get() }
    // 不返回敏感信息
    delete values.openid
    return values
  }

  // 类方法
  User.findByOpenid = function(openid) {
    return this.findOne({
      where: { openid }
    })
  }

  User.findBTypeUsers = function(options = {}) {
    return this.findAll({
      where: {
        user_type: 1,
        ...options.where
      },
      ...options
    })
  }

  User.findCTypeUsers = function(options = {}) {
    return this.findAll({
      where: {
        user_type: 0,
        ...options.where
      },
      ...options
    })
  }

  User.findPendingLicenseUsers = function(options = {}) {
    return this.findAll({
      where: {
        license_status: 0,
        business_license: {
          [sequelize.Sequelize.Op.ne]: ''
        },
        ...options.where
      },
      ...options
    })
  }

  return User
}
