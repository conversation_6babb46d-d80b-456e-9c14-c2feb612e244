{"name": "ddlpicker-admin-web", "version": "1.0.0", "description": "刀刀乐捡漏网管理后台", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.9", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "sortablejs": "^1.15.0", "vue-cropper": "^1.0.3", "xlsx": "^0.18.5", "file-saver": "^2.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "sass": "^1.64.1", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "eslint-config-prettier": "^8.8.0", "prettier": "^3.0.0", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.8", "@types/sortablejs": "^1.15.1", "@types/file-saver": "^2.0.5", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "unplugin-icons": "^0.16.5", "@iconify/json": "^2.2.82"}, "engines": {"node": ">=16.0.0"}}