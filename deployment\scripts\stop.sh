#!/bin/bash

# 刀刀乐捡漏网停止脚本
# 使用方法: ./stop.sh [选项]
# 选项: --clean (清理容器和镜像)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

PROJECT_NAME="ddlpicker"
CLEAN_MODE=false

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            CLEAN_MODE=true
            shift
            ;;
        -h|--help)
            echo "使用方法: $0 [选项]"
            echo "选项:"
            echo "  --clean    停止服务并清理容器和镜像"
            echo "  -h, --help 显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            exit 1
            ;;
    esac
done

log_info "停止 ${PROJECT_NAME} 服务..."

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null; then
    log_error "docker-compose 命令未找到，请先安装"
    exit 1
fi

# 检查是否有运行的服务
if ! docker-compose ps | grep -q "Up"; then
    log_warning "没有运行中的服务"
else
    # 停止所有服务
    log_info "停止所有服务..."
    docker-compose down
    
    if [ $? -eq 0 ]; then
        log_success "服务停止成功"
    else
        log_error "服务停止失败"
        exit 1
    fi
fi

# 清理模式
if [ "$CLEAN_MODE" = true ]; then
    log_warning "清理模式已启用，将删除容器、镜像和卷..."
    
    # 确认操作
    read -p "确定要清理所有容器、镜像和数据卷吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理容器和网络..."
        docker-compose down --volumes --remove-orphans
        
        log_info "清理项目相关镜像..."
        docker images | grep "${PROJECT_NAME}" | awk '{print $3}' | xargs -r docker rmi -f
        
        log_info "清理未使用的资源..."
        docker system prune -f
        
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
fi

# 显示剩余的容器和镜像
log_info "当前Docker状态:"
echo "容器:"
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo
echo "镜像:"
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

log_success "停止脚本执行完成！"
