#!/bin/bash

# 刀刀乐捡漏网启动脚本
# 使用方法: ./start.sh [环境]
# 例如: ./start.sh development

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-development}
PROJECT_NAME="ddlpicker"

log_info "启动 ${PROJECT_NAME} - ${ENVIRONMENT} 环境"

# 检查Docker和Docker Compose
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

log_info "检查必要的命令..."
check_command docker
check_command docker-compose

# 检查环境文件
if [ ! -f ".env" ]; then
    log_warning ".env 文件不存在，从示例文件复制..."
    cp .env.example .env
    log_warning "请编辑 .env 文件，填入正确的配置后重新运行"
    exit 1
fi

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p backend-api/logs
mkdir -p backend-api/uploads
mkdir -p deployment/nginx/logs
mkdir -p deployment/ssl

# 根据环境设置不同的配置
case $ENVIRONMENT in
    development)
        log_info "启动开发环境..."
        # 只启动数据库和Redis
        docker-compose up -d mysql redis
        
        log_info "等待数据库启动..."
        sleep 20
        
        # 检查数据库连接
        if docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD:-ddlpicker123} -e "SELECT 1" > /dev/null 2>&1; then
            log_success "数据库连接成功"
        else
            log_error "数据库连接失败"
            exit 1
        fi
        
        log_info "开发环境启动完成！"
        log_info "请分别启动后端和前端服务："
        log_info "  后端: cd backend-api && npm run dev"
        log_info "  前端: cd admin-web && npm run dev"
        ;;
        
    staging|production)
        log_info "启动 ${ENVIRONMENT} 环境..."
        
        # 检查SSL证书
        if [ ! -f "deployment/ssl/ddlpicker.com.crt" ] || [ ! -f "deployment/ssl/ddlpicker.com.key" ]; then
            log_warning "SSL证书文件不存在，将使用HTTP模式"
        fi
        
        # 启动所有服务
        docker-compose up -d
        
        log_info "等待服务启动..."
        sleep 30
        
        # 健康检查
        log_info "进行健康检查..."
        
        # 检查后端API
        if curl -f http://localhost:3000/health > /dev/null 2>&1; then
            log_success "后端API健康检查通过"
        else
            log_error "后端API健康检查失败"
        fi
        
        # 检查管理端Web
        if curl -f http://localhost:8080 > /dev/null 2>&1; then
            log_success "管理端Web健康检查通过"
        else
            log_error "管理端Web健康检查失败"
        fi
        
        # 检查Nginx
        if curl -f http://localhost > /dev/null 2>&1; then
            log_success "Nginx健康检查通过"
        else
            log_error "Nginx健康检查失败"
        fi
        
        log_success "${ENVIRONMENT} 环境启动完成！"
        ;;
        
    *)
        log_error "不支持的环境: ${ENVIRONMENT}"
        log_error "支持的环境: development, staging, production"
        exit 1
        ;;
esac

# 显示服务状态
log_info "服务状态:"
docker-compose ps

# 显示访问地址
log_info "服务访问地址:"
case $ENVIRONMENT in
    development)
        log_info "  - MySQL: localhost:3306"
        log_info "  - Redis: localhost:6379"
        log_info "  - 后端API: http://localhost:3000 (需手动启动)"
        log_info "  - 管理后台: http://localhost:8080 (需手动启动)"
        ;;
    staging|production)
        log_info "  - API: http://localhost:3000"
        log_info "  - 管理后台: http://localhost:8080"
        log_info "  - 主站: http://localhost"
        if [ -f "deployment/ssl/ddlpicker.com.crt" ]; then
            log_info "  - HTTPS主站: https://localhost"
        fi
        ;;
esac

# 显示日志查看命令
log_info "查看日志:"
log_info "  docker-compose logs -f [服务名]"
log_info "  可用服务: mysql, redis, backend-api, admin-web, nginx"

log_success "启动脚本执行完成！"
