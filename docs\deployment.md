# 刀刀乐捡漏网部署指南

## 环境要求

### 服务器配置
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上SSD
- **操作系统**: CentOS 8 / Ubuntu 20.04 LTS
- **网络**: 公网IP，带宽5Mbps以上

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.0+
- Nginx 1.18+

## 快速部署

### 1. 服务器准备

```bash
# 更新系统
sudo yum update -y  # CentOS
# 或
sudo apt update && sudo apt upgrade -y  # Ubuntu

# 安装Docker
curl -fsSL https://get.docker.com | bash
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Git
sudo yum install git -y  # CentOS
# 或
sudo apt install git -y  # Ubuntu
```

### 2. 克隆项目

```bash
# 克隆项目到服务器
git clone https://github.com/your-username/DaoDLE.git
cd DaoDLE

# 设置执行权限
chmod +x deployment/scripts/deploy.sh
```

### 3. 配置环境变量

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

**重要配置项说明：**

```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_PASSWORD=your_secure_password

# JWT密钥（必须修改）
JWT_SECRET=your_very_secure_jwt_secret_key

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
WECHAT_MCH_ID=your_merchant_id
WECHAT_API_KEY=your_wechat_pay_key

# 阿里云OSS配置
OSS_ACCESS_KEY_ID=your_oss_access_key
OSS_ACCESS_KEY_SECRET=your_oss_secret_key
OSS_BUCKET=your_bucket_name

# 域名配置
DOMAIN=your-domain.com
ADMIN_DOMAIN=admin.your-domain.com
```

### 4. SSL证书配置

```bash
# 创建SSL证书目录
sudo mkdir -p deployment/ssl

# 将SSL证书文件放入目录
# deployment/ssl/ddlpicker.com.crt
# deployment/ssl/ddlpicker.com.key

# 或使用Let's Encrypt免费证书
sudo apt install certbot -y
sudo certbot certonly --standalone -d your-domain.com -d admin.your-domain.com
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem deployment/ssl/ddlpicker.com.crt
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem deployment/ssl/ddlpicker.com.key
```

### 5. 执行部署

```bash
# 生产环境部署
./deployment/scripts/deploy.sh production

# 或指定版本部署
./deployment/scripts/deploy.sh production v1.0.0
```

## 手动部署步骤

如果自动部署脚本出现问题，可以按以下步骤手动部署：

### 1. 构建镜像

```bash
# 构建后端API镜像
docker build -t ddlpicker-backend-api:latest ./backend-api/

# 构建管理端Web镜像
docker build -t ddlpicker-admin-web:latest ./admin-web/
```

### 2. 启动服务

```bash
# 启动数据库和Redis
docker-compose up -d mysql redis

# 等待数据库启动
sleep 30

# 初始化数据库
docker-compose exec mysql mysql -u root -p${MYSQL_ROOT_PASSWORD} < database/init.sql

# 启动所有服务
docker-compose up -d
```

### 3. 验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend-api
docker-compose logs -f admin-web

# 健康检查
curl http://localhost:3000/health
curl http://localhost:8080
```

## 域名配置

### DNS设置

在域名服务商处添加以下DNS记录：

```
A记录: your-domain.com -> 服务器IP
A记录: admin.your-domain.com -> 服务器IP
A记录: api.your-domain.com -> 服务器IP
```

### 防火墙配置

```bash
# CentOS 8
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload

# Ubuntu
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 监控和维护

### 日志查看

```bash
# 查看应用日志
docker-compose logs -f backend-api
docker-compose logs -f admin-web
docker-compose logs -f nginx

# 查看系统日志
sudo journalctl -u docker
```

### 数据备份

```bash
# 数据库备份
docker-compose exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD} ddlpicker > backup_$(date +%Y%m%d).sql

# Redis备份
docker-compose exec redis redis-cli BGSAVE
```

### 服务重启

```bash
# 重启单个服务
docker-compose restart backend-api

# 重启所有服务
docker-compose restart

# 更新服务
docker-compose pull
docker-compose up -d
```

## 性能优化

### 1. 数据库优化

```sql
-- 添加索引
ALTER TABLE users ADD INDEX idx_user_type_status (user_type, license_status);
ALTER TABLE order_master ADD INDEX idx_user_status_time (user_id, status, created_at);
ALTER TABLE sku ADD INDEX idx_spu_stock (spu_id, stock);

-- 分区表维护
ALTER TABLE order_master ADD PARTITION (
    PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01'))
);
```

### 2. Redis缓存配置

```bash
# 编辑Redis配置
vim deployment/redis/redis.conf

# 添加以下配置
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. Nginx优化

```nginx
# 编辑Nginx配置
vim deployment/nginx/nginx.conf

# 添加缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m;

# 在location块中添加
proxy_cache api_cache;
proxy_cache_valid 200 5m;
proxy_cache_key "$scheme$request_method$host$request_uri";
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec mysql mysql -u root -p -e "SELECT 1"
   
   # 重置数据库密码
   docker-compose exec mysql mysql -u root -p -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';"
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   docker-compose exec redis redis-cli ping
   
   # 查看Redis日志
   docker-compose logs redis
   ```

3. **SSL证书问题**
   ```bash
   # 检查证书有效期
   openssl x509 -in deployment/ssl/ddlpicker.com.crt -text -noout
   
   # 测试SSL配置
   openssl s_client -connect your-domain.com:443
   ```

4. **内存不足**
   ```bash
   # 查看内存使用
   free -h
   docker stats
   
   # 清理Docker资源
   docker system prune -a
   ```

### 回滚操作

```bash
# 查看备份
ls -la /backup/ddlpicker/

# 回滚到指定版本
./deployment/scripts/rollback.sh backup_20241201_120000

# 或手动回滚
docker-compose down
cp -r /backup/ddlpicker/backup_20241201_120000/* ./
docker-compose up -d
```

## 安全建议

1. **定期更新系统和Docker**
2. **使用强密码和密钥**
3. **启用防火墙**
4. **定期备份数据**
5. **监控系统资源和日志**
6. **使用HTTPS和安全头**
7. **限制数据库和Redis的网络访问**

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看日志文件
2. 检查配置文件
3. 参考故障排除章节
4. 联系技术支持团队
