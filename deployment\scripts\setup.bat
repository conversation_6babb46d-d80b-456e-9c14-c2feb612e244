@echo off
REM 刀刀乐捡漏网Windows环境设置脚本
REM 使用方法: setup.bat [环境]
REM 环境: development, production

setlocal enabledelayedexpansion

REM 颜色定义
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 设置环境
set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=development

echo %BLUE%[INFO]%NC% 开始设置刀刀乐捡漏网 - %ENVIRONMENT% 环境
echo.

REM 检查Node.js
echo %BLUE%[INFO]%NC% 检查Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Node.js未安装，请先安装Node.js 16+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo %GREEN%[SUCCESS]%NC% Node.js已安装
)

REM 检查npm
echo %BLUE%[INFO]%NC% 检查npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% npm未安装
    pause
    exit /b 1
) else (
    echo %GREEN%[SUCCESS]%NC% npm已安装
)

REM 检查Git
echo %BLUE%[INFO]%NC% 检查Git...
git --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Git未安装，建议安装Git进行版本控制
) else (
    echo %GREEN%[SUCCESS]%NC% Git已安装
)

REM 创建必要的目录
echo %BLUE%[INFO]%NC% 创建必要的目录...
if not exist "backend-api\logs" mkdir "backend-api\logs"
if not exist "backend-api\uploads" mkdir "backend-api\uploads"
if not exist "deployment\ssl" mkdir "deployment\ssl"
if not exist "deployment\nginx\logs" mkdir "deployment\nginx\logs"
echo %GREEN%[SUCCESS]%NC% 目录创建完成

REM 复制环境配置文件
echo %BLUE%[INFO]%NC% 设置环境配置...
if not exist ".env" (
    copy ".env.example" ".env"
    echo %GREEN%[SUCCESS]%NC% 根目录.env文件已创建
) else (
    echo %YELLOW%[WARNING]%NC% 根目录.env文件已存在
)

if not exist "backend-api\.env" (
    copy "backend-api\.env.example" "backend-api\.env"
    echo %GREEN%[SUCCESS]%NC% 后端.env文件已创建
) else (
    echo %YELLOW%[WARNING]%NC% 后端.env文件已存在
)

if not exist "admin-web\.env" (
    copy "admin-web\.env.example" "admin-web\.env"
    echo %GREEN%[SUCCESS]%NC% 前端.env文件已创建
) else (
    echo %YELLOW%[WARNING]%NC% 前端.env文件已存在
)

REM 安装依赖
echo %BLUE%[INFO]%NC% 安装项目依赖...
echo 正在安装根目录依赖...
call npm install
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 根目录依赖安装失败
    pause
    exit /b 1
)

echo 正在安装后端依赖...
cd backend-api
call npm install
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 后端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo 正在安装前端依赖...
cd admin-web
call npm install
if errorlevel 1 (
    echo %RED%[ERROR]%NC% 前端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo %GREEN%[SUCCESS]%NC% 所有依赖安装完成

REM 检查Docker（可选）
echo %BLUE%[INFO]%NC% 检查Docker...
docker --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Docker未安装，无法使用容器化部署
    echo 如需使用Docker，请安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
) else (
    echo %GREEN%[SUCCESS]%NC% Docker已安装
    
    REM 检查Docker Compose
    docker-compose --version >nul 2>&1
    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% Docker Compose未安装
    ) else (
        echo %GREEN%[SUCCESS]%NC% Docker Compose已安装
    )
)

REM 显示下一步操作
echo.
echo %GREEN%[SUCCESS]%NC% 环境设置完成！
echo.
echo %BLUE%[INFO]%NC% 下一步操作:
echo.
echo 1. 配置环境变量:
echo    - 编辑 .env 文件，填入数据库等配置
echo    - 编辑 backend-api\.env 文件
echo    - 编辑 admin-web\.env 文件
echo.
echo 2. 设置数据库:
echo    - 安装MySQL 8.0
echo    - 创建数据库: CREATE DATABASE ddlpicker;
echo    - 导入初始数据: mysql -u root -p ddlpicker ^< database\init.sql
echo.
echo 3. 启动服务:
echo    开发环境:
echo      后端: cd backend-api ^&^& npm run dev
echo      前端: cd admin-web ^&^& npm run dev
echo.
echo    生产环境:
echo      Docker: docker-compose up -d
echo.
echo 4. 访问应用:
echo    - 后端API: http://localhost:3000
echo    - 管理后台: http://localhost:8080
echo    - 小程序: 使用微信开发者工具打开miniprogram目录
echo.
echo %BLUE%[INFO]%NC% 详细文档请查看 docs\quick-start.md
echo.

if "%ENVIRONMENT%"=="development" (
    echo %YELLOW%[提示]%NC% 开发环境设置完成，请按照上述步骤继续配置
) else (
    echo %YELLOW%[提示]%NC% 生产环境设置完成，请确保配置安全的密码和密钥
)

pause
