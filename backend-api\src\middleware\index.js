const jwt = require('jsonwebtoken')
const config = require('../config')
const { AdminUser } = require('../models')
const log = require('../utils/logger')

// 错误处理中间件
const errorHandler = () => {
  return async (ctx, next) => {
    try {
      await next()
    } catch (error) {
      log.error('请求错误:', error)
      
      // 设置状态码
      ctx.status = error.status || error.statusCode || 500
      
      // 错误响应
      ctx.body = {
        success: false,
        message: error.message || '服务器内部错误',
        code: ctx.status,
        ...(config.env === 'development' && { stack: error.stack })
      }
      
      // 触发应用级错误事件
      ctx.app.emit('error', error, ctx)
    }
  }
}

// 响应处理中间件
const responseHandler = () => {
  return async (ctx, next) => {
    await next()
    
    // 如果已经设置了响应体，则不处理
    if (ctx.body !== undefined) {
      return
    }
    
    // 默认成功响应
    ctx.body = {
      success: true,
      message: 'success',
      data: null
    }
  }
}

// JWT认证中间件
const auth = (options = {}) => {
  return async (ctx, next) => {
    try {
      const token = ctx.headers.authorization?.replace('Bearer ', '')
      
      if (!token) {
        ctx.throw(401, '未提供认证令牌')
      }
      
      const decoded = jwt.verify(token, config.jwt.secret)
      
      // 查询用户信息
      const user = await AdminUser.findByPk(decoded.id)
      if (!user) {
        ctx.throw(401, '用户不存在')
      }
      
      // 将用户信息添加到上下文
      ctx.state.user = user
      ctx.state.userId = user.id
      
      await next()
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        ctx.throw(401, '无效的认证令牌')
      } else if (error.name === 'TokenExpiredError') {
        ctx.throw(401, '认证令牌已过期')
      } else {
        throw error
      }
    }
  }
}

// 权限检查中间件
const permission = (requiredPermissions = []) => {
  return async (ctx, next) => {
    const user = ctx.state.user
    
    if (!user) {
      ctx.throw(401, '未认证')
    }
    
    // 超级管理员拥有所有权限
    if (user.role === 'super_admin') {
      await next()
      return
    }
    
    // 检查权限
    const userPermissions = user.permissions || []
    const hasPermission = requiredPermissions.every(permission => 
      userPermissions.includes(permission) || userPermissions.includes('*')
    )
    
    if (!hasPermission) {
      ctx.throw(403, '权限不足')
    }
    
    await next()
  }
}

// 参数验证中间件
const validate = (schema, source = 'body') => {
  return async (ctx, next) => {
    try {
      let data
      
      switch (source) {
        case 'body':
          data = ctx.request.body
          break
        case 'query':
          data = ctx.query
          break
        case 'params':
          data = ctx.params
          break
        default:
          data = ctx.request.body
      }
      
      const { error, value } = schema.validate(data, {
        abortEarly: false,
        allowUnknown: true,
        stripUnknown: true
      })
      
      if (error) {
        const message = error.details.map(detail => detail.message).join('; ')
        ctx.throw(400, `参数验证失败: ${message}`)
      }
      
      // 将验证后的数据重新赋值
      if (source === 'body') {
        ctx.request.body = value
      } else if (source === 'query') {
        ctx.query = value
      } else if (source === 'params') {
        ctx.params = value
      }
      
      await next()
    } catch (error) {
      throw error
    }
  }
}

// 分页中间件
const pagination = () => {
  return async (ctx, next) => {
    const page = parseInt(ctx.query.page) || config.pagination.defaultPage
    const pageSize = Math.min(
      parseInt(ctx.query.pageSize) || config.pagination.defaultPageSize,
      config.pagination.maxPageSize
    )
    
    ctx.state.pagination = {
      page,
      pageSize,
      offset: (page - 1) * pageSize,
      limit: pageSize
    }
    
    await next()
  }
}

// 审计日志中间件
const auditLog = (action, targetType) => {
  return async (ctx, next) => {
    await next()
    
    // 记录审计日志
    try {
      const { AuditLog } = require('../models')
      
      await AuditLog.create({
        operator: ctx.state.user?.username || 'system',
        action,
        target_type: targetType,
        target_id: ctx.params.id || null,
        detail_json: {
          method: ctx.method,
          url: ctx.url,
          body: ctx.request.body,
          query: ctx.query,
          ip: ctx.ip,
          userAgent: ctx.headers['user-agent']
        }
      })
    } catch (error) {
      log.error('记录审计日志失败:', error)
    }
  }
}

// 缓存中间件
const cache = (ttl = config.cache.ttl) => {
  return async (ctx, next) => {
    const redis = ctx.app.redis
    if (!redis) {
      await next()
      return
    }
    
    const key = `${config.cache.prefix}${ctx.method}:${ctx.url}`
    
    try {
      // 尝试从缓存获取
      const cached = await redis.get(key)
      if (cached) {
        ctx.body = JSON.parse(cached)
        return
      }
      
      await next()
      
      // 缓存响应
      if (ctx.status === 200 && ctx.body) {
        await redis.setex(key, ttl, JSON.stringify(ctx.body))
      }
    } catch (error) {
      log.error('缓存操作失败:', error)
      await next()
    }
  }
}

// 请求ID中间件
const requestId = () => {
  return async (ctx, next) => {
    const requestId = ctx.headers['x-request-id'] || 
      `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    ctx.state.requestId = requestId
    ctx.set('X-Request-ID', requestId)
    
    await next()
  }
}

module.exports = {
  errorHandler,
  responseHandler,
  auth,
  permission,
  validate,
  pagination,
  auditLog,
  cache,
  requestId
}
