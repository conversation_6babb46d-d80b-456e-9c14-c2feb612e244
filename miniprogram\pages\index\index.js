// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    banners: [],
    categories: [],
    seckillProducts: [],
    products: [],
    page: 1,
    pageSize: 20,
    hasMore: true,
    loading: false,
    showBackTop: false,
    countdown: '00:00:00'
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    // 更新用户信息
    this.setData({
      userInfo: app.globalData.userInfo
    })
    
    // 更新购物车数量
    this.updateCartCount()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  onPageScroll(e) {
    // 显示/隐藏返回顶部按钮
    const showBackTop = e.scrollTop > 500
    if (showBackTop !== this.data.showBackTop) {
      this.setData({ showBackTop })
    }
  },

  // 初始化页面
  async initPage() {
    wx.showLoading({ title: '加载中...' })
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadCategories(),
        this.loadSeckillProducts(),
        this.loadProducts()
      ])
      
      // 启动秒杀倒计时
      this.startCountdown()
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      page: 1,
      products: [],
      hasMore: true
    })
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadSeckillProducts(),
        this.loadProducts()
      ])
    } catch (error) {
      console.error('刷新失败:', error)
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 加载轮播图
  async loadBanners() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getBanners'
      })
      
      this.setData({
        banners: res.result.data || []
      })
    } catch (error) {
      console.error('加载轮播图失败:', error)
    }
  },

  // 加载分类
  async loadCategories() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCategories',
        data: {
          parent_id: 0,
          limit: 8
        }
      })
      
      this.setData({
        categories: res.result.data || []
      })
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  },

  // 加载秒杀商品
  async loadSeckillProducts() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getSeckillProducts'
      })
      
      this.setData({
        seckillProducts: res.result.data || []
      })
    } catch (error) {
      console.error('加载秒杀商品失败:', error)
    }
  },

  // 加载商品列表
  async loadProducts() {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'getProducts',
        data: {
          page: this.data.page,
          pageSize: this.data.pageSize,
          user_type: this.data.userInfo?.user_type || 0
        }
      })
      
      const newProducts = res.result.data || []
      const hasMore = newProducts.length === this.data.pageSize
      
      this.setData({
        products: this.data.page === 1 ? newProducts : [...this.data.products, ...newProducts],
        hasMore,
        page: this.data.page + 1
      })
    } catch (error) {
      console.error('加载商品失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载更多
  loadMore() {
    this.loadProducts()
  },

  // 更新购物车数量
  async updateCartCount() {
    if (!this.data.userInfo) return
    
    try {
      const res = await wx.cloud.callFunction({
        name: 'getCartCount',
        data: {
          user_id: this.data.userInfo.openid
        }
      })
      
      app.globalData.cartCount = res.result.count || 0
      
      // 更新tabBar徽标
      if (app.globalData.cartCount > 0) {
        wx.setTabBarBadge({
          index: 2,
          text: app.globalData.cartCount.toString()
        })
      } else {
        wx.removeTabBarBadge({ index: 2 })
      }
    } catch (error) {
      console.error('更新购物车数量失败:', error)
    }
  },

  // 启动秒杀倒计时
  startCountdown() {
    const updateCountdown = () => {
      const now = new Date()
      const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
      const diff = tomorrow.getTime() - now.getTime()
      
      if (diff > 0) {
        const hours = Math.floor(diff / (1000 * 60 * 60))
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((diff % (1000 * 60)) / 1000)
        
        this.setData({
          countdown: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        })
      }
    }
    
    updateCountdown()
    this.countdownTimer = setInterval(updateCountdown, 1000)
  },

  // 页面事件处理
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  onBannerTap(e) {
    const url = e.currentTarget.dataset.url
    if (url) {
      // 处理轮播图跳转
      console.log('跳转到:', url)
    }
  },

  goToCategory(e) {
    const id = e.currentTarget.dataset.id
    wx.switchTab({
      url: `/pages/category/category?id=${id}`
    })
  },

  goToUpgrade() {
    wx.navigateTo({
      url: '/pages/upgrade-b/upgrade-b'
    })
  },

  goToSeckill() {
    wx.navigateTo({
      url: '/pages/seckill/seckill'
    })
  },

  goToProduct(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${id}`
    })
  },

  backToTop() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })
  },

  onUnload() {
    // 清除定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  }
})
