const Koa = require('koa')
const bodyParser = require('koa-bodyparser')
const cors = require('koa-cors')
const helmet = require('koa-helmet')
const logger = require('koa-logger')
const serve = require('koa-static')
const path = require('path')
const rateLimit = require('koa-ratelimit')
const Redis = require('redis')

// 导入配置
const config = require('./config')
const { sequelize } = require('./models')
const routes = require('./routes')
const { errorHandler, responseHandler } = require('./middleware')
const log = require('./utils/logger')

// 创建应用实例
const app = new Koa()

// 创建Redis客户端
const redis = Redis.createClient({
  host: config.redis.host,
  port: config.redis.port,
  password: config.redis.password,
  db: config.redis.db
})

redis.on('error', (err) => {
  log.error('Redis连接错误:', err)
})

redis.on('connect', () => {
  log.info('Redis连接成功')
})

// 连接Redis
redis.connect().catch(err => {
  log.error('Redis连接失败:', err)
})

// 全局中间件
app.use(helmet()) // 安全头
app.use(cors({
  origin: config.cors.origin,
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept']
}))

// 请求日志
if (config.env !== 'test') {
  app.use(logger())
}

// 限流中间件
app.use(rateLimit({
  driver: 'redis',
  db: redis,
  duration: 60000, // 1分钟
  errorMessage: '请求过于频繁，请稍后再试',
  id: (ctx) => ctx.ip,
  headers: {
    remaining: 'Rate-Limit-Remaining',
    reset: 'Rate-Limit-Reset',
    total: 'Rate-Limit-Total'
  },
  max: 100, // 每分钟最多100次请求
  disableHeader: false
}))

// 请求体解析
app.use(bodyParser({
  enableTypes: ['json', 'form'],
  jsonLimit: '10mb',
  formLimit: '10mb',
  textLimit: '10mb'
}))

// 静态文件服务
app.use(serve(path.join(__dirname, '../public')))

// 响应处理中间件
app.use(responseHandler())

// 错误处理中间件
app.use(errorHandler())

// 路由
app.use(routes.routes())
app.use(routes.allowedMethods())

// 404处理
app.use(async (ctx) => {
  ctx.status = 404
  ctx.body = {
    success: false,
    message: '接口不存在',
    code: 404
  }
})

// 全局错误处理
app.on('error', (err, ctx) => {
  log.error('应用错误:', err)
  if (ctx) {
    log.error('请求上下文:', {
      method: ctx.method,
      url: ctx.url,
      headers: ctx.headers
    })
  }
})

// 优雅关闭
process.on('SIGTERM', async () => {
  log.info('收到SIGTERM信号，开始优雅关闭...')
  
  try {
    // 关闭数据库连接
    await sequelize.close()
    log.info('数据库连接已关闭')
    
    // 关闭Redis连接
    await redis.quit()
    log.info('Redis连接已关闭')
    
    process.exit(0)
  } catch (error) {
    log.error('优雅关闭失败:', error)
    process.exit(1)
  }
})

process.on('SIGINT', async () => {
  log.info('收到SIGINT信号，开始优雅关闭...')
  
  try {
    await sequelize.close()
    await redis.quit()
    process.exit(0)
  } catch (error) {
    log.error('优雅关闭失败:', error)
    process.exit(1)
  }
})

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    await sequelize.authenticate()
    log.info('数据库连接成功')
    
    // 同步数据库模型（仅在开发环境）
    if (config.env === 'development') {
      await sequelize.sync({ alter: true })
      log.info('数据库模型同步完成')
    }
    
    // 启动HTTP服务器
    const server = app.listen(config.port, config.host, () => {
      log.info(`服务器启动成功: http://${config.host}:${config.port}`)
      log.info(`环境: ${config.env}`)
      log.info(`API文档: http://${config.host}:${config.port}/api-docs`)
    })
    
    // 设置服务器超时
    server.timeout = 30000 // 30秒
    
    return server
  } catch (error) {
    log.error('服务器启动失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer()
}

module.exports = app
