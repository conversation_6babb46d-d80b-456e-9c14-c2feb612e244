{"name": "ddlpicker-backend-api", "version": "1.0.0", "description": "刀刀乐捡漏网后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build": "echo 'No build step required'", "test": "jest", "migrate": "node src/scripts/migrate.js", "seed": "node src/scripts/seed.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["nodejs", "koa", "mysql", "redis", "jwt", "api"], "author": "AI-PM", "license": "MIT", "dependencies": {"koa": "^2.14.2", "koa-router": "^12.0.0", "koa-bodyparser": "^4.4.1", "koa-cors": "^0.0.16", "koa-helmet": "^7.0.2", "koa-logger": "^3.2.1", "koa-ratelimit": "^5.1.0", "koa-static": "^5.0.0", "koa-jwt": "^4.0.4", "koa2-swagger-ui": "^5.10.0", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "redis": "^4.6.7", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "moment": "^2.29.4", "lodash": "^4.17.21", "axios": "^1.4.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.4", "qrcode": "^1.5.3", "node-cron": "^3.0.2", "winston": "^3.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0"}}