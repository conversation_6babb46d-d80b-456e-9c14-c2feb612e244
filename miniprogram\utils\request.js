// 微信小程序请求工具类
const app = getApp()

class Request {
  constructor() {
    this.baseURL = app.globalData.baseUrl
    this.timeout = 10000
    this.header = {
      'Content-Type': 'application/json'
    }
  }

  // 请求拦截器
  interceptors = {
    request: (config) => {
      // 添加loading
      if (config.loading !== false) {
        wx.showLoading({
          title: config.loadingText || '加载中...',
          mask: true
        })
      }

      // 添加token
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo && userInfo.token) {
        config.header = {
          ...config.header,
          'Authorization': `Bearer ${userInfo.token}`
        }
      }

      return config
    },

    response: (response, config) => {
      // 隐藏loading
      if (config.loading !== false) {
        wx.hideLoading()
      }

      // 处理响应
      if (response.statusCode === 200) {
        const data = response.data
        
        if (data.success) {
          return data
        } else {
          // 业务错误
          if (config.showError !== false) {
            wx.showToast({
              title: data.message || '请求失败',
              icon: 'none'
            })
          }
          return Promise.reject(data)
        }
      } else {
        // HTTP错误
        const errorMsg = this.getErrorMessage(response.statusCode)
        if (config.showError !== false) {
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          })
        }
        return Promise.reject({
          statusCode: response.statusCode,
          message: errorMsg
        })
      }
    }
  }

  // 获取错误信息
  getErrorMessage(statusCode) {
    const errorMap = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '权限不足',
      404: '请求的资源不存在',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时'
    }
    return errorMap[statusCode] || '网络错误'
  }

  // 基础请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      // 合并配置
      const config = {
        url: this.baseURL + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          ...this.header,
          ...options.header
        },
        timeout: options.timeout || this.timeout,
        loading: options.loading,
        loadingText: options.loadingText,
        showError: options.showError
      }

      // 请求拦截
      const interceptedConfig = this.interceptors.request(config)

      // 发送请求
      wx.request({
        ...interceptedConfig,
        success: (response) => {
          this.interceptors.response(response, config)
            .then(resolve)
            .catch(reject)
        },
        fail: (error) => {
          // 隐藏loading
          if (config.loading !== false) {
            wx.hideLoading()
          }

          // 网络错误
          const errorMsg = '网络连接失败，请检查网络'
          if (config.showError !== false) {
            wx.showToast({
              title: errorMsg,
              icon: 'none'
            })
          }
          reject({
            type: 'network',
            message: errorMsg,
            error
          })
        }
      })
    })
  }

  // GET请求
  get(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      data,
      ...options
    })
  }

  // POST请求
  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  // PUT请求
  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  // DELETE请求
  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }

  // 文件上传
  upload(url, filePath, options = {}) {
    return new Promise((resolve, reject) => {
      // 添加loading
      if (options.loading !== false) {
        wx.showLoading({
          title: options.loadingText || '上传中...',
          mask: true
        })
      }

      // 添加token
      const userInfo = wx.getStorageSync('userInfo')
      const header = { ...options.header }
      if (userInfo && userInfo.token) {
        header['Authorization'] = `Bearer ${userInfo.token}`
      }

      wx.uploadFile({
        url: this.baseURL + url,
        filePath,
        name: options.name || 'file',
        formData: options.formData || {},
        header,
        success: (response) => {
          // 隐藏loading
          if (options.loading !== false) {
            wx.hideLoading()
          }

          try {
            const data = JSON.parse(response.data)
            if (data.success) {
              resolve(data)
            } else {
              if (options.showError !== false) {
                wx.showToast({
                  title: data.message || '上传失败',
                  icon: 'none'
                })
              }
              reject(data)
            }
          } catch (error) {
            if (options.showError !== false) {
              wx.showToast({
                title: '上传失败',
                icon: 'none'
              })
            }
            reject(error)
          }
        },
        fail: (error) => {
          // 隐藏loading
          if (options.loading !== false) {
            wx.hideLoading()
          }

          if (options.showError !== false) {
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
          reject(error)
        }
      })
    })
  }
}

// 创建实例
const request = new Request()

module.exports = request
