/* pages/index/index.wxss */

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.search-input {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
}

.search-placeholder {
  margin-left: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 轮播图 */
.banner-section {
  margin-bottom: 20rpx;
}

.banner-swiper {
  height: 300rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类导航 */
.category-nav {
  padding: 30rpx;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
}

/* 用户身份提示 */
.user-type-tip {
  padding: 30rpx;
}

.tip-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tip-left {
  flex: 1;
}

.tip-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.tip-desc {
  font-size: 24rpx;
  color: #666;
}

/* 秒杀活动 */
.seckill-section {
  padding: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.countdown {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.section-more {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 24rpx;
}

.seckill-scroll {
  white-space: nowrap;
}

.seckill-list {
  display: flex;
  gap: 20rpx;
}

.seckill-item {
  flex-shrink: 0;
  width: 200rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.seckill-image {
  width: 100%;
  height: 200rpx;
}

.seckill-info {
  padding: 20rpx;
}

.seckill-price {
  color: #ff4757;
  font-weight: 600;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.seckill-original-price {
  color: #999;
  font-size: 20rpx;
  text-decoration: line-through;
}

/* 推荐商品 */
.recommend-section {
  margin-top: 20rpx;
}

.recommend-section .section-header {
  padding: 0 30rpx 20rpx;
  background: #fff;
  margin-bottom: 0;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  background: #fff;
}

.product-item {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.product-image-wrapper {
  position: relative;
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-tag {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.price {
  color: #ff4757;
  font-weight: 600;
  font-size: 28rpx;
}

.price-symbol {
  font-size: 20rpx;
}

.price-original {
  color: #999;
  font-size: 20rpx;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.product-sales {
  font-size: 20rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-more-btn {
  color: #666;
  font-size: 28rpx;
}

.no-more {
  padding: 40rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

/* 返回顶部 */
.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: 150rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s;
  z-index: 999;
}

.back-to-top.show {
  opacity: 1;
  transform: translateY(0);
}

/* 图标字体 */
.iconfont {
  font-family: "iconfont";
}

.icon-arrow-right::before {
  content: ">";
}

.icon-arrow-up::before {
  content: "↑";
}
