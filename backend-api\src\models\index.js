const { Sequelize } = require('sequelize')
const config = require('../config')

// 创建Sequelize实例
const sequelize = new Sequelize(
  config.database.database,
  config.database.username,
  config.database.password,
  config.database
)

// 导入模型
const User = require('./User')(sequelize, Sequelize.DataTypes)
const Role = require('./Role')(sequelize, Sequelize.DataTypes)
const UserRole = require('./UserRole')(sequelize, Sequelize.DataTypes)
const UserAddress = require('./UserAddress')(sequelize, Sequelize.DataTypes)
const Category = require('./Category')(sequelize, Sequelize.DataTypes)
const Brand = require('./Brand')(sequelize, Sequelize.DataTypes)
const Spu = require('./Spu')(sequelize, Sequelize.DataTypes)
const Sku = require('./Sku')(sequelize, Sequelize.DataTypes)
const Cart = require('./Cart')(sequelize, Sequelize.DataTypes)
const OrderMaster = require('./OrderMaster')(sequelize, Sequelize.DataTypes)
const OrderItem = require('./OrderItem')(sequelize, Sequelize.DataTypes)
const PaymentLog = require('./PaymentLog')(sequelize, Sequelize.DataTypes)
const RefundOrder = require('./RefundOrder')(sequelize, Sequelize.DataTypes)
const CouponTemplate = require('./CouponTemplate')(sequelize, Sequelize.DataTypes)
const UserCoupon = require('./UserCoupon')(sequelize, Sequelize.DataTypes)
const FreightTemplate = require('./FreightTemplate')(sequelize, Sequelize.DataTypes)
const FreightArea = require('./FreightArea')(sequelize, Sequelize.DataTypes)
const SystemConfig = require('./SystemConfig')(sequelize, Sequelize.DataTypes)
const AdminUser = require('./AdminUser')(sequelize, Sequelize.DataTypes)
const AuditLog = require('./AuditLog')(sequelize, Sequelize.DataTypes)

// 定义关联关系
const defineAssociations = () => {
  // 用户和角色的多对多关系
  User.belongsToMany(Role, {
    through: UserRole,
    foreignKey: 'user_id',
    otherKey: 'role_id',
    as: 'roles'
  })
  Role.belongsToMany(User, {
    through: UserRole,
    foreignKey: 'role_id',
    otherKey: 'user_id',
    as: 'users'
  })

  // 用户和地址的一对多关系
  User.hasMany(UserAddress, {
    foreignKey: 'user_id',
    as: 'addresses'
  })
  UserAddress.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  })

  // 分类的自关联
  Category.hasMany(Category, {
    foreignKey: 'parent_id',
    as: 'children'
  })
  Category.belongsTo(Category, {
    foreignKey: 'parent_id',
    as: 'parent'
  })

  // SPU和分类的关系
  Spu.belongsTo(Category, {
    foreignKey: 'category_id',
    as: 'category'
  })
  Category.hasMany(Spu, {
    foreignKey: 'category_id',
    as: 'spus'
  })

  // SPU和品牌的关系
  Spu.belongsTo(Brand, {
    foreignKey: 'brand_id',
    as: 'brand'
  })
  Brand.hasMany(Spu, {
    foreignKey: 'brand_id',
    as: 'spus'
  })

  // SPU和SKU的一对多关系
  Spu.hasMany(Sku, {
    foreignKey: 'spu_id',
    as: 'skus'
  })
  Sku.belongsTo(Spu, {
    foreignKey: 'spu_id',
    as: 'spu'
  })

  // 用户和购物车的关系
  User.hasMany(Cart, {
    foreignKey: 'user_id',
    as: 'carts'
  })
  Cart.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  })

  // 购物车和SKU的关系
  Cart.belongsTo(Sku, {
    foreignKey: 'sku_id',
    as: 'sku'
  })
  Sku.hasMany(Cart, {
    foreignKey: 'sku_id',
    as: 'carts'
  })

  // 用户和订单的关系
  User.hasMany(OrderMaster, {
    foreignKey: 'user_id',
    as: 'orders'
  })
  OrderMaster.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  })

  // 订单和订单项的关系
  OrderMaster.hasMany(OrderItem, {
    foreignKey: 'order_id',
    as: 'items'
  })
  OrderItem.belongsTo(OrderMaster, {
    foreignKey: 'order_id',
    as: 'order'
  })

  // 订单项和SKU的关系
  OrderItem.belongsTo(Sku, {
    foreignKey: 'sku_id',
    as: 'sku'
  })
  Sku.hasMany(OrderItem, {
    foreignKey: 'sku_id',
    as: 'orderItems'
  })

  // 订单和支付流水的关系
  OrderMaster.hasMany(PaymentLog, {
    foreignKey: 'order_id',
    as: 'payments'
  })
  PaymentLog.belongsTo(OrderMaster, {
    foreignKey: 'order_id',
    as: 'order'
  })

  // 订单和退款单的关系
  OrderMaster.hasMany(RefundOrder, {
    foreignKey: 'order_id',
    as: 'refunds'
  })
  RefundOrder.belongsTo(OrderMaster, {
    foreignKey: 'order_id',
    as: 'order'
  })

  // 用户和优惠券的关系
  User.hasMany(UserCoupon, {
    foreignKey: 'user_id',
    as: 'coupons'
  })
  UserCoupon.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  })

  // 优惠券模板和用户优惠券的关系
  CouponTemplate.hasMany(UserCoupon, {
    foreignKey: 'template_id',
    as: 'userCoupons'
  })
  UserCoupon.belongsTo(CouponTemplate, {
    foreignKey: 'template_id',
    as: 'template'
  })

  // 运费模板和运费区域的关系
  FreightTemplate.hasMany(FreightArea, {
    foreignKey: 'template_id',
    as: 'areas'
  })
  FreightArea.belongsTo(FreightTemplate, {
    foreignKey: 'template_id',
    as: 'template'
  })

  // SPU和运费模板的关系
  Spu.belongsTo(FreightTemplate, {
    foreignKey: 'freight_template_id',
    as: 'freightTemplate'
  })
  FreightTemplate.hasMany(Spu, {
    foreignKey: 'freight_template_id',
    as: 'spus'
  })
}

// 执行关联定义
defineAssociations()

module.exports = {
  sequelize,
  Sequelize,
  User,
  Role,
  UserRole,
  UserAddress,
  Category,
  Brand,
  Spu,
  Sku,
  Cart,
  OrderMaster,
  OrderItem,
  PaymentLog,
  RefundOrder,
  CouponTemplate,
  UserCoupon,
  FreightTemplate,
  FreightArea,
  SystemConfig,
  AdminUser,
  AuditLog
}
