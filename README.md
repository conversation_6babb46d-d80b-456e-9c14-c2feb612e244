# 刀刀乐捡漏网 (DDLPicker)

B2B2C 微信小程序电商系统

## 项目概述

刀刀乐捡漏网是一个基于微信小程序的B2B2C电商平台，支持C端用户和B端批发商在同一平台进行购物。

### 核心特性

- **双重身份**: C端用户可通过实名认证和营业执照上传升级为B端用户
- **差异化价格**: B端用户享受批发价格，C端用户享受零售价格
- **微信生态**: 完全基于微信小程序和微信云开发
- **管理后台**: 提供完整的Web管理后台

## 技术架构

### 前端
- **小程序端**: 微信小程序 + 微信云开发
- **管理端**: Vue3 + ElementPlus + Vite + Pinia

### 后端
- **API服务**: Node.js + Koa2 + JWT
- **数据库**: MySQL 8.0 + Redis 6
- **云服务**: 微信云开发 (云函数、云数据库、云存储)

### 部署
- **容器化**: Docker + Docker Compose
- **Web服务器**: Nginx
- **云平台**: 腾讯云 Lighthouse

## 项目结构

```
DaoDLE/
├── miniprogram/              # 微信小程序端
│   ├── pages/               # 页面
│   ├── components/          # 组件
│   ├── cloudfunctions/      # 云函数
│   └── utils/               # 工具类
├── admin-web/               # 管理端Web
│   ├── src/                 # 源码
│   ├── public/              # 静态资源
│   └── dist/                # 构建产物
├── backend-api/             # 后端API服务
│   ├── src/                 # 源码
│   ├── config/              # 配置
│   └── tests/               # 测试
├── database/                # 数据库脚本
│   ├── migrations/          # 迁移脚本
│   └── seeds/               # 初始数据
├── deployment/              # 部署配置
│   ├── docker/              # Docker配置
│   ├── nginx/               # Nginx配置
│   └── scripts/             # 部署脚本
└── docs/                    # 文档
```

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0
- Docker >= 20.10
- 微信开发者工具

### 安装依赖

```bash
# 安装后端依赖
cd backend-api
npm install

# 安装管理端依赖
cd ../admin-web
npm install
```

### 数据库初始化

```bash
# 创建数据库
mysql -u root -p < database/init.sql

# 运行迁移
cd backend-api
npm run migrate
```

### 启动服务

```bash
# 启动后端API
cd backend-api
npm run dev

# 启动管理端
cd admin-web
npm run dev
```

### 小程序开发

1. 使用微信开发者工具打开 `miniprogram` 目录
2. 配置小程序AppID
3. 开启云开发环境

## 开发指南

详细的开发指南请参考 [docs/development.md](docs/development.md)

## 部署指南

详细的部署指南请参考 [docs/deployment.md](docs/deployment.md)

## API文档

API文档地址: http://localhost:3000/api-docs

## 许可证

MIT License
