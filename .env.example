# 环境配置示例文件
# 复制此文件为 .env 并填入实际配置值

# 应用环境
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 数据库配置
MYSQL_ROOT_PASSWORD=your_mysql_root_password
MYSQL_DATABASE=ddlpicker
MYSQL_USER=ddlpicker
MYSQL_PASSWORD=your_mysql_password
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=ddlpicker
DB_PASSWORD=your_mysql_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=2h
JWT_REFRESH_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
WECHAT_MCH_ID=your_wechat_mch_id
WECHAT_API_KEY=your_wechat_api_key
WECHAT_NOTIFY_URL=https://ddlpicker.com/api/v1/payment/wechat/notify
WECHAT_CERT_PATH=/path/to/wechat/cert.pem
WECHAT_KEY_PATH=/path/to/wechat/key.pem

# 阿里云OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET=your_oss_bucket_name
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com

# 短信配置
SMS_ACCESS_KEY_ID=your_sms_access_key_id
SMS_ACCESS_KEY_SECRET=your_sms_access_key_secret
SMS_SIGN_NAME=刀刀乐捡漏网
SMS_TEMPLATE_CODE=SMS_123456789

# CORS配置
CORS_ORIGIN=https://admin.ddlpicker.com,http://localhost:8080

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_PATH=/app/uploads

# 日志配置
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=ddlpicker:

# 安全配置
BCRYPT_ROUNDS=10
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# 业务配置
ORDER_AUTO_CLOSE_TIME=30
ORDER_AUTO_CONFIRM_TIME=7
COUPON_EXPIRE_REMIND_TIME=3

# 监控配置
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_PORT=9090

# 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=<EMAIL>

# 腾讯云配置
TENCENT_SECRET_ID=your_tencent_secret_id
TENCENT_SECRET_KEY=your_tencent_secret_key
TENCENT_REGION=ap-guangzhou

# 域名配置
DOMAIN=ddlpicker.com
ADMIN_DOMAIN=admin.ddlpicker.com
API_DOMAIN=api.ddlpicker.com

# SSL证书路径
SSL_CERT_PATH=/etc/nginx/ssl/ddlpicker.com.crt
SSL_KEY_PATH=/etc/nginx/ssl/ddlpicker.com.key
