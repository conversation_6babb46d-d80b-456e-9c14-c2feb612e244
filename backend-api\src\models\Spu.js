module.exports = (sequelize, DataTypes) => {
  const Spu = sequelize.define('Spu', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: 'SPU ID'
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '商品名称'
    },
    subtitle: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '副标题'
    },
    category_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '分类ID'
    },
    brand_id: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '品牌ID'
    },
    main_pic_url: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '主图地址'
    },
    video_url: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '视频地址'
    },
    status: {
      type: DataTypes.TINYINT,
      defaultValue: 1,
      comment: '状态：0-下架 1-上架 2-预售'
    },
    freight_template_id: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: '运费模板ID'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    tableName: 'spu',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['category_id']
      },
      {
        fields: ['brand_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['name']
      }
    ],
    comment: 'SPU商品表'
  })

  // 类方法
  Spu.findOnSale = function(options = {}) {
    return this.findAll({
      where: {
        status: 1,
        ...options.where
      },
      ...options
    })
  }

  Spu.findByCategory = function(categoryId, options = {}) {
    return this.findAll({
      where: {
        category_id: categoryId,
        status: 1,
        ...options.where
      },
      ...options
    })
  }

  Spu.searchByName = function(keyword, options = {}) {
    return this.findAll({
      where: {
        name: {
          [sequelize.Sequelize.Op.like]: `%${keyword}%`
        },
        status: 1,
        ...options.where
      },
      ...options
    })
  }

  return Spu
}
