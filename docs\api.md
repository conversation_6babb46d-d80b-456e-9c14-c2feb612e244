# 刀刀乐捡漏网 API 文档

## 接口概述

### 基础信息
- **基础URL**: `https://api.ddlpicker.com/api/v1`
- **认证方式**: JWT <PERSON>er <PERSON>
- **请求格式**: JSON
- **响应格式**: JSON

### 通用响应格式

**成功响应:**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "code": 200,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应:**
```json
{
  "success": false,
  "message": "错误信息",
  "code": 400,
  "errors": [
    {
      "field": "字段名",
      "message": "错误详情"
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 状态码说明
- `200` - 成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 认证接口

### 管理员登录
```
POST /auth/login
```

**请求参数:**
```json
{
  "username": "admin",
  "password": "password"
}
```

**响应数据:**
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "super_admin"
    }
  }
}
```

### 刷新Token
```
POST /auth/refresh
```

**请求头:**
```
Authorization: Bearer refresh_token
```

### 退出登录
```
POST /auth/logout
```

## 用户管理

### 获取用户列表
```
GET /users?page=1&pageSize=20&user_type=0&keyword=搜索关键词
```

**查询参数:**
- `page` - 页码，默认1
- `pageSize` - 每页数量，默认20，最大100
- `user_type` - 用户类型：0-C端，1-B端，2-员工
- `keyword` - 搜索关键词（昵称、手机号）

**响应数据:**
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "用户昵称",
        "avatar_url": "头像地址",
        "phone": "13800138000",
        "user_type": 0,
        "license_status": 0,
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20
  }
}
```

### 获取用户详情
```
GET /users/:id
```

### 审核营业执照
```
POST /users/:id/audit-license
```

**请求参数:**
```json
{
  "status": 1,
  "reason": "审核通过"
}
```

## 商品管理

### 获取商品列表
```
GET /products?page=1&pageSize=20&category_id=1&status=1&keyword=商品名称
```

**查询参数:**
- `category_id` - 分类ID
- `status` - 状态：0-下架，1-上架，2-预售
- `keyword` - 搜索关键词

### 创建商品
```
POST /products
```

**请求参数:**
```json
{
  "name": "商品名称",
  "subtitle": "副标题",
  "category_id": 1,
  "brand_id": 1,
  "main_pic_url": "主图地址",
  "status": 1,
  "skus": [
    {
      "spec_json": "{\"颜色\":\"红色\",\"尺寸\":\"L\"}",
      "price_c": 99.00,
      "price_b": 89.00,
      "stock": 100,
      "bar_code": "条形码"
    }
  ]
}
```

### 更新商品
```
PUT /products/:id
```

### 删除商品
```
DELETE /products/:id
```

## 订单管理

### 获取订单列表
```
GET /orders?page=1&pageSize=20&status=1&user_id=1&start_date=2024-01-01&end_date=2024-01-31
```

**查询参数:**
- `status` - 订单状态：0-待付款，1-待发货，2-已发货，3-已收货，4-已完成，5-已取消
- `user_id` - 用户ID
- `start_date` - 开始日期
- `end_date` - 结束日期

### 获取订单详情
```
GET /orders/:id
```

### 发货
```
POST /orders/:id/ship
```

**请求参数:**
```json
{
  "express_name": "顺丰速运",
  "express_no": "SF1234567890"
}
```

### 修改订单价格
```
POST /orders/:id/change-price
```

**请求参数:**
```json
{
  "new_amount": 88.00,
  "reason": "价格调整原因"
}
```

## 分类管理

### 获取分类树
```
GET /categories/tree
```

### 创建分类
```
POST /categories
```

**请求参数:**
```json
{
  "name": "分类名称",
  "parent_id": 0,
  "sort_order": 1,
  "icon_url": "图标地址"
}
```

## 优惠券管理

### 获取优惠券模板列表
```
GET /coupons/templates
```

### 创建优惠券模板
```
POST /coupons/templates
```

**请求参数:**
```json
{
  "title": "满100减10",
  "type": 0,
  "value": 10.00,
  "min_amount": 100.00,
  "total_count": 1000,
  "start_time": "2024-01-01T00:00:00.000Z",
  "end_time": "2024-12-31T23:59:59.000Z"
}
```

## 数据统计

### 获取仪表盘数据
```
GET /dashboard/stats
```

**响应数据:**
```json
{
  "success": true,
  "data": {
    "todayOrders": 156,
    "todayRevenue": 12580.50,
    "totalUsers": 8520,
    "totalProducts": 1250,
    "orderTrend": [
      { "date": "2024-01-01", "count": 120, "amount": 8500.00 }
    ],
    "userTrend": [
      { "date": "2024-01-01", "count": 50 }
    ]
  }
}
```

### 获取销售报表
```
GET /reports/sales?start_date=2024-01-01&end_date=2024-01-31&group_by=day
```

**查询参数:**
- `group_by` - 分组方式：day-按天，week-按周，month-按月

## 系统配置

### 获取系统配置
```
GET /system/config
```

### 更新系统配置
```
PUT /system/config
```

**请求参数:**
```json
{
  "site_name": "刀刀乐捡漏网",
  "wechat_appid": "微信小程序AppID",
  "oss_bucket": "OSS存储桶名称"
}
```

## 文件上传

### 上传图片
```
POST /upload/image
```

**请求格式:** `multipart/form-data`

**请求参数:**
- `file` - 图片文件
- `type` - 图片类型：product-商品图，avatar-头像，license-营业执照

**响应数据:**
```json
{
  "success": true,
  "data": {
    "url": "https://oss.example.com/images/xxx.jpg",
    "filename": "xxx.jpg",
    "size": 102400
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 10001 | 参数验证失败 |
| 10002 | 用户不存在 |
| 10003 | 密码错误 |
| 10004 | Token无效 |
| 10005 | Token已过期 |
| 20001 | 商品不存在 |
| 20002 | 库存不足 |
| 30001 | 订单不存在 |
| 30002 | 订单状态错误 |
| 40001 | 文件上传失败 |
| 40002 | 文件格式不支持 |

## 接口限流

- 普通接口：每分钟100次
- 登录接口：每分钟5次
- 上传接口：每分钟20次

## 示例代码

### JavaScript (Axios)
```javascript
import axios from 'axios'

const api = axios.create({
  baseURL: 'https://api.ddlpicker.com/api/v1',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 获取用户列表
const getUserList = async (params) => {
  const response = await api.get('/users', { params })
  return response.data
}
```

### cURL
```bash
# 登录
curl -X POST https://api.ddlpicker.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'

# 获取用户列表
curl -X GET "https://api.ddlpicker.com/api/v1/users?page=1&pageSize=20" \
  -H "Authorization: Bearer your_token_here"
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持用户管理、商品管理、订单管理等基础功能

## 联系方式

如有API相关问题，请联系：
- 技术支持：<EMAIL>
- API文档：https://docs.ddlpicker.com
