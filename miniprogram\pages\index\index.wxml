<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar" bindtap="goToSearch">
    <view class="search-input">
      <icon type="search" size="16" color="#999"></icon>
      <text class="search-placeholder">搜索商品</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" bindtap="onBannerTap" data-url="{{item.url}}"></image>
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-nav card">
    <view class="category-grid">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="goToCategory" data-id="{{item.id}}">
        <image class="category-icon" src="{{item.icon_url}}" mode="aspectFit"></image>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 用户身份提示 -->
  <view class="user-type-tip card" wx:if="{{userInfo}}">
    <view class="tip-content">
      <view class="tip-left">
        <text class="tip-title">当前身份：{{userInfo.user_type === 1 ? 'B端批发商' : 'C端用户'}}</text>
        <text class="tip-desc" wx:if="{{userInfo.user_type === 0}}">上传营业执照可升级为B端，享受批发价</text>
        <text class="tip-desc" wx:else>您正在享受批发价格</text>
      </view>
      <view class="tip-right" wx:if="{{userInfo.user_type === 0}}">
        <button class="btn btn-primary btn-small" bindtap="goToUpgrade">立即升级</button>
      </view>
    </view>
  </view>

  <!-- 秒杀活动 -->
  <view class="seckill-section card" wx:if="{{seckillProducts.length > 0}}">
    <view class="section-header">
      <view class="section-title">
        <text class="title-text">限时秒杀</text>
        <view class="countdown">
          <text class="countdown-text">{{countdown}}</text>
        </view>
      </view>
      <view class="section-more" bindtap="goToSeckill">
        <text>更多</text>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
    <scroll-view class="seckill-scroll" scroll-x="{{true}}">
      <view class="seckill-list">
        <view class="seckill-item" wx:for="{{seckillProducts}}" wx:key="id" bindtap="goToProduct" data-id="{{item.id}}">
          <image class="seckill-image" src="{{item.main_pic_url}}" mode="aspectFill"></image>
          <view class="seckill-info">
            <view class="seckill-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.seckill_price}}</text>
            </view>
            <view class="seckill-original-price">¥{{item.original_price}}</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐商品 -->
  <view class="recommend-section">
    <view class="section-header">
      <view class="section-title">
        <text class="title-text">为你推荐</text>
      </view>
    </view>
    <view class="product-grid">
      <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="goToProduct" data-id="{{item.id}}">
        <view class="product-image-wrapper">
          <image class="product-image" src="{{item.main_pic_url}}" mode="aspectFill"></image>
          <view class="product-tag" wx:if="{{item.tag}}">{{item.tag}}</view>
        </view>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-price">
            <text class="price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{userInfo && userInfo.user_type === 1 ? item.price_b : item.price_c}}</text>
            </text>
            <text class="price-original" wx:if="{{item.original_price}}">¥{{item.original_price}}</text>
          </view>
          <view class="product-sales">已售{{item.sales}}件</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    <view class="load-more-btn" wx:else bindtap="loadMore">
      <text>加载更多</text>
    </view>
  </view>

  <!-- 到底了 -->
  <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
    <text>没有更多商品了</text>
  </view>
</view>

<!-- 返回顶部 -->
<view class="back-to-top {{showBackTop ? 'show' : ''}}" bindtap="backToTop">
  <text class="iconfont icon-arrow-up"></text>
</view>
