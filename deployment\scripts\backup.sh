#!/bin/bash

# 刀刀乐捡漏网数据备份脚本
# 使用方法: ./backup.sh [类型]
# 类型: database, files, all

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
PROJECT_NAME="ddlpicker"
BACKUP_TYPE=${1:-all}
BACKUP_DIR="/backup/${PROJECT_NAME}"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="${PROJECT_NAME}_${DATE}"

# 创建备份目录
mkdir -p ${BACKUP_DIR}

log_info "开始备份 ${PROJECT_NAME} - 类型: ${BACKUP_TYPE}"

# 数据库备份
backup_database() {
    log_info "备份数据库..."
    
    # 获取数据库配置
    DB_HOST=${DB_HOST:-localhost}
    DB_PORT=${DB_PORT:-3306}
    DB_USER=${DB_USER:-ddlpicker}
    DB_PASSWORD=${DB_PASSWORD:-ddlpicker123}
    DB_NAME=${DB_NAME:-ddlpicker}
    
    # 备份数据库
    MYSQL_BACKUP_FILE="${BACKUP_DIR}/${BACKUP_NAME}_mysql.sql"
    
    if command -v docker-compose &> /dev/null && docker-compose ps mysql | grep -q "Up"; then
        # 使用Docker容器备份
        docker-compose exec mysql mysqldump -u root -p${MYSQL_ROOT_PASSWORD:-ddlpicker123} ${DB_NAME} > ${MYSQL_BACKUP_FILE}
    else
        # 直接备份
        mysqldump -h ${DB_HOST} -P ${DB_PORT} -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} > ${MYSQL_BACKUP_FILE}
    fi
    
    if [ $? -eq 0 ]; then
        # 压缩备份文件
        gzip ${MYSQL_BACKUP_FILE}
        log_success "数据库备份完成: ${MYSQL_BACKUP_FILE}.gz"
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# Redis备份
backup_redis() {
    log_info "备份Redis..."
    
    REDIS_BACKUP_DIR="${BACKUP_DIR}/${BACKUP_NAME}_redis"
    mkdir -p ${REDIS_BACKUP_DIR}
    
    if command -v docker-compose &> /dev/null && docker-compose ps redis | grep -q "Up"; then
        # 使用Docker容器备份
        docker-compose exec redis redis-cli BGSAVE
        sleep 5
        docker cp $(docker-compose ps -q redis):/data/dump.rdb ${REDIS_BACKUP_DIR}/
    else
        # 直接备份
        redis-cli BGSAVE
        sleep 5
        cp /var/lib/redis/dump.rdb ${REDIS_BACKUP_DIR}/
    fi
    
    if [ -f "${REDIS_BACKUP_DIR}/dump.rdb" ]; then
        tar -czf ${REDIS_BACKUP_DIR}.tar.gz -C ${BACKUP_DIR} ${BACKUP_NAME}_redis
        rm -rf ${REDIS_BACKUP_DIR}
        log_success "Redis备份完成: ${REDIS_BACKUP_DIR}.tar.gz"
    else
        log_error "Redis备份失败"
        return 1
    fi
}

# 文件备份
backup_files() {
    log_info "备份文件..."
    
    FILES_BACKUP_FILE="${BACKUP_DIR}/${BACKUP_NAME}_files.tar.gz"
    
    # 备份上传文件和日志
    tar -czf ${FILES_BACKUP_FILE} \
        --exclude='node_modules' \
        --exclude='.git' \
        --exclude='dist' \
        --exclude='*.log' \
        backend-api/uploads/ \
        backend-api/logs/ \
        deployment/ssl/ \
        .env 2>/dev/null || true
    
    if [ -f "${FILES_BACKUP_FILE}" ]; then
        log_success "文件备份完成: ${FILES_BACKUP_FILE}"
    else
        log_error "文件备份失败"
        return 1
    fi
}

# 代码备份
backup_code() {
    log_info "备份代码..."
    
    CODE_BACKUP_FILE="${BACKUP_DIR}/${BACKUP_NAME}_code.tar.gz"
    
    # 备份源代码
    tar -czf ${CODE_BACKUP_FILE} \
        --exclude='node_modules' \
        --exclude='.git' \
        --exclude='dist' \
        --exclude='logs' \
        --exclude='uploads' \
        --exclude='*.log' \
        . 2>/dev/null || true
    
    if [ -f "${CODE_BACKUP_FILE}" ]; then
        log_success "代码备份完成: ${CODE_BACKUP_FILE}"
    else
        log_error "代码备份失败"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    # 保留最近7天的备份
    find ${BACKUP_DIR} -name "${PROJECT_NAME}_*" -type f -mtime +7 -delete
    
    log_success "旧备份清理完成"
}

# 生成备份报告
generate_report() {
    REPORT_FILE="${BACKUP_DIR}/${BACKUP_NAME}_report.txt"
    
    cat > ${REPORT_FILE} << EOF
刀刀乐捡漏网备份报告
==================

备份时间: $(date)
备份类型: ${BACKUP_TYPE}
备份目录: ${BACKUP_DIR}

备份文件:
EOF
    
    ls -lh ${BACKUP_DIR}/${BACKUP_NAME}_* >> ${REPORT_FILE}
    
    echo "" >> ${REPORT_FILE}
    echo "备份完成时间: $(date)" >> ${REPORT_FILE}
    
    log_success "备份报告生成: ${REPORT_FILE}"
}

# 主备份流程
main() {
    case ${BACKUP_TYPE} in
        database)
            backup_database
            backup_redis
            ;;
        files)
            backup_files
            backup_code
            ;;
        all)
            backup_database
            backup_redis
            backup_files
            backup_code
            ;;
        *)
            log_error "不支持的备份类型: ${BACKUP_TYPE}"
            log_error "支持的类型: database, files, all"
            exit 1
            ;;
    esac
    
    cleanup_old_backups
    generate_report
    
    log_success "备份完成！"
    log_info "备份文件位置: ${BACKUP_DIR}"
}

# 检查权限
if [ "$EUID" -ne 0 ] && [ ! -w ${BACKUP_DIR%/*} ]; then
    log_error "权限不足，请使用sudo运行或确保有写入权限"
    exit 1
fi

# 执行备份
main

log_success "备份脚本执行完成！"
