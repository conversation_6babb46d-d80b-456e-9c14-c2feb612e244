import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import App from './App.vue'
import router from './router'
import { useUserStore } from './stores/user'

// 样式
import './styles/index.scss'

// 全局组件
import './components/global'

// 权限控制
import './permission'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 全局属性
app.config.globalProperties.$ELEMENT = {
  size: 'default'
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
  
  // 可以在这里添加错误上报逻辑
  if (process.env.NODE_ENV === 'production') {
    // 上报错误到监控系统
  }
}

// 挂载应用
app.mount('#app')

// 开发环境下的调试工具
if (process.env.NODE_ENV === 'development') {
  window.__app__ = app
  window.__router__ = router
  window.__pinia__ = app.config.globalProperties.$pinia
}
