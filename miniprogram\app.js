// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // env 参数说明：
        //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
        //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
        //   如不填则使用默认环境（第一个创建的环境）
        env: 'ddlpicker-prod', // 请替换为你的云环境ID
        traceUser: true,
      })
    }

    // 检查登录状态
    this.checkLoginStatus()
    
    // 获取系统信息
    this.getSystemInfo()
  },

  onShow() {
    // 小程序显示时检查更新
    this.checkUpdate()
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo && userInfo.openid) {
      this.globalData.userInfo = userInfo
      this.globalData.isLogin = true
    } else {
      this.globalData.isLogin = false
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        // 计算状态栏高度
        this.globalData.statusBarHeight = res.statusBarHeight
        // 计算导航栏高度
        this.globalData.navBarHeight = res.statusBarHeight + 44
      }
    })
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(() => {
            wx.showModal({
              title: '更新提示',
              content: '新版本已经准备好，是否重启应用？',
              success: (res) => {
                if (res.confirm) {
                  updateManager.applyUpdate()
                }
              }
            })
          })
          updateManager.onUpdateFailed(() => {
            wx.showModal({
              title: '更新失败',
              content: '新版本下载失败，请检查网络后重试',
              showCancel: false
            })
          })
        }
      })
    }
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 调用云函数获取openid
            wx.cloud.callFunction({
              name: 'getOpenId',
              data: {
                code: res.code
              },
              success: (cloudRes) => {
                const { openid, unionid } = cloudRes.result
                // 获取用户信息
                wx.getUserProfile({
                  desc: '用于完善用户资料',
                  success: (userRes) => {
                    const userInfo = {
                      openid,
                      unionid,
                      nickname: userRes.userInfo.nickName,
                      avatar: userRes.userInfo.avatarUrl,
                      user_type: 0 // 默认C端用户
                    }
                    
                    // 保存到本地存储
                    wx.setStorageSync('userInfo', userInfo)
                    this.globalData.userInfo = userInfo
                    this.globalData.isLogin = true
                    
                    resolve(userInfo)
                  },
                  fail: reject
                })
              },
              fail: reject
            })
          } else {
            reject(new Error('登录失败'))
          }
        },
        fail: reject
      })
    })
  },

  // 退出登录
  logout() {
    wx.removeStorageSync('userInfo')
    this.globalData.userInfo = null
    this.globalData.isLogin = false
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    })
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading()
  },

  // 显示消息提示
  showToast(title, icon = 'none', duration = 2000) {
    wx.showToast({
      title,
      icon,
      duration
    })
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLogin: false,
    systemInfo: null,
    statusBarHeight: 0,
    navBarHeight: 0,
    cartCount: 0, // 购物车数量
    baseUrl: 'https://api.ddlpicker.com' // API基础地址
  }
})
