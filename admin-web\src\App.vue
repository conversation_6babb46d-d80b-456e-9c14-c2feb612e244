<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

const userStore = useUserStore()
const appStore = useAppStore()

onMounted(() => {
  // 初始化应用
  initApp()
})

const initApp = async () => {
  try {
    // 检查登录状态
    await userStore.checkLoginStatus()
    
    // 初始化应用配置
    await appStore.initApp()
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
}
</script>

<style lang="scss">
#app {
  height: 100vh;
  overflow: hidden;
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// Element Plus 样式覆盖
.el-message {
  min-width: 300px;
}

.el-notification {
  min-width: 330px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

// 表格样式
.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 表单样式
.el-form {
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

// 按钮样式
.el-button {
  &.is-link {
    padding: 0;
    height: auto;
    border: none;
    background: none;
  }
}

// 卡片样式
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 18px 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 分页样式
.el-pagination {
  margin-top: 20px;
  text-align: right;
}

// 对话框样式
.el-dialog {
  border-radius: 8px;
  
  .el-dialog__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 20px 15px;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    border-top: 1px solid #f0f0f0;
    padding: 15px 20px 20px;
    text-align: right;
  }
}

// 抽屉样式
.el-drawer {
  .el-drawer__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px;
    margin-bottom: 0;
    
    .el-drawer__title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-drawer__body {
    padding: 20px;
  }
}

// 标签页样式
.el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;
  }
  
  .el-tabs__nav-wrap {
    &::after {
      background-color: #f0f0f0;
    }
  }
  
  .el-tabs__item {
    font-weight: 500;
    
    &.is-active {
      color: #409eff;
    }
  }
}

// 菜单样式
.el-menu {
  border-right: none;
  
  .el-menu-item,
  .el-submenu__title {
    height: 48px;
    line-height: 48px;
    
    &:hover {
      background-color: #ecf5ff;
      color: #409eff;
    }
  }
  
  .el-menu-item.is-active {
    background-color: #409eff;
    color: #fff;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: #fff;
    }
  }
}

// 面包屑样式
.el-breadcrumb {
  .el-breadcrumb__item {
    .el-breadcrumb__inner {
      font-weight: 400;
      color: #606266;
      
      &.is-link {
        color: #409eff;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }
    
    &:last-child {
      .el-breadcrumb__inner {
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

// 工具提示样式
.el-tooltip__popper {
  max-width: 300px;
}

// 加载样式
.el-loading-spinner {
  .circular {
    width: 42px;
    height: 42px;
  }
}

// 空状态样式
.el-empty {
  .el-empty__image {
    width: 120px;
    height: 120px;
  }
  
  .el-empty__description {
    color: #909399;
    font-size: 14px;
  }
}
</style>
