// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { openid, unionid } = wxContext
    
    // 查询用户是否已存在
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()
    
    let userInfo = null
    
    if (userResult.data.length > 0) {
      // 用户已存在，返回用户信息
      userInfo = userResult.data[0]
    } else {
      // 新用户，创建用户记录
      const createResult = await db.collection('users').add({
        data: {
          openid: openid,
          unionid: unionid || null,
          nickname: '',
          avatar_url: '',
          phone: '',
          user_type: 0, // 默认C端用户
          business_license: '',
          license_status: 0,
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      
      if (createResult._id) {
        userInfo = {
          _id: createResult._id,
          openid: openid,
          unionid: unionid || null,
          user_type: 0,
          license_status: 0
        }
      }
    }
    
    return {
      success: true,
      openid: openid,
      unionid: unionid,
      userInfo: userInfo
    }
  } catch (error) {
    console.error('getOpenId error:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
