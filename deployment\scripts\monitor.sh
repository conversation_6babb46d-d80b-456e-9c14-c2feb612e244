#!/bin/bash

# 刀刀乐捡漏网服务监控脚本
# 使用方法: ./monitor.sh [选项]
# 选项: --check, --restart, --status, --logs

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

PROJECT_NAME="ddlpicker"
ACTION=${1:-status}

# 检查服务状态
check_service_status() {
    local service=$1
    local port=$2
    local endpoint=$3
    
    log_info "检查 ${service} 服务状态..."
    
    # 检查容器状态
    if docker-compose ps ${service} | grep -q "Up"; then
        log_success "${service} 容器运行正常"
        
        # 检查端口
        if [ -n "$port" ]; then
            if netstat -tuln | grep -q ":${port}"; then
                log_success "${service} 端口 ${port} 监听正常"
            else
                log_error "${service} 端口 ${port} 未监听"
                return 1
            fi
        fi
        
        # 检查健康状态
        if [ -n "$endpoint" ]; then
            if curl -f -s ${endpoint} > /dev/null; then
                log_success "${service} 健康检查通过"
            else
                log_error "${service} 健康检查失败"
                return 1
            fi
        fi
        
        return 0
    else
        log_error "${service} 容器未运行"
        return 1
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存使用
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
        log_warning "内存使用率过高: ${MEMORY_USAGE}%"
    else
        log_success "内存使用率正常: ${MEMORY_USAGE}%"
    fi
    
    # 检查磁盘使用
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 80 ]; then
        log_warning "磁盘使用率过高: ${DISK_USAGE}%"
    else
        log_success "磁盘使用率正常: ${DISK_USAGE}%"
    fi
    
    # 检查CPU负载
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    CPU_CORES=$(nproc)
    if (( $(echo "$LOAD_AVG > $CPU_CORES" | bc -l) )); then
        log_warning "CPU负载过高: ${LOAD_AVG} (核心数: ${CPU_CORES})"
    else
        log_success "CPU负载正常: ${LOAD_AVG} (核心数: ${CPU_CORES})"
    fi
}

# 检查Docker资源
check_docker_resources() {
    log_info "检查Docker资源..."
    
    # 检查容器资源使用
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | grep -E "(mysql|redis|backend-api|admin-web|nginx)"
    
    # 检查磁盘空间
    DOCKER_SIZE=$(docker system df | grep "Total" | awk '{print $4}')
    log_info "Docker占用空间: ${DOCKER_SIZE}"
}

# 重启服务
restart_service() {
    local service=$1
    
    log_info "重启 ${service} 服务..."
    
    docker-compose restart ${service}
    
    if [ $? -eq 0 ]; then
        log_success "${service} 重启成功"
        sleep 10
        check_service_status ${service}
    else
        log_error "${service} 重启失败"
        return 1
    fi
}

# 查看日志
view_logs() {
    local service=$1
    local lines=${2:-50}
    
    log_info "查看 ${service} 最近 ${lines} 行日志..."
    docker-compose logs --tail=${lines} ${service}
}

# 全面健康检查
full_health_check() {
    log_info "开始全面健康检查..."
    
    local all_healthy=true
    
    # 检查各个服务
    check_service_status "mysql" "3306" || all_healthy=false
    check_service_status "redis" "6379" || all_healthy=false
    check_service_status "backend-api" "3000" "http://localhost:3000/health" || all_healthy=false
    check_service_status "admin-web" "8080" "http://localhost:8080" || all_healthy=false
    check_service_status "nginx" "80" "http://localhost" || all_healthy=false
    
    # 检查系统资源
    check_system_resources
    
    # 检查Docker资源
    check_docker_resources
    
    if [ "$all_healthy" = true ]; then
        log_success "所有服务健康检查通过"
        return 0
    else
        log_error "部分服务健康检查失败"
        return 1
    fi
}

# 自动修复
auto_repair() {
    log_info "开始自动修复..."
    
    # 检查并重启失败的服务
    for service in mysql redis backend-api admin-web nginx; do
        if ! check_service_status ${service} > /dev/null 2>&1; then
            log_warning "${service} 服务异常，尝试重启..."
            restart_service ${service}
        fi
    done
    
    # 清理Docker资源
    log_info "清理Docker资源..."
    docker system prune -f
    
    log_success "自动修复完成"
}

# 生成监控报告
generate_report() {
    local report_file="/tmp/${PROJECT_NAME}_monitor_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "刀刀乐捡漏网监控报告"
        echo "===================="
        echo "生成时间: $(date)"
        echo ""
        
        echo "服务状态:"
        docker-compose ps
        echo ""
        
        echo "系统资源:"
        echo "内存使用: $(free -h | grep Mem | awk '{print $3"/"$2}')"
        echo "磁盘使用: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5")"}')"
        echo "CPU负载: $(uptime | awk -F'load average:' '{print $2}')"
        echo ""
        
        echo "Docker资源:"
        docker system df
        echo ""
        
        echo "网络连接:"
        netstat -tuln | grep -E ":(80|443|3000|3306|6379|8080)"
        
    } > ${report_file}
    
    log_success "监控报告生成: ${report_file}"
    cat ${report_file}
}

# 主函数
main() {
    case ${ACTION} in
        --check|check)
            full_health_check
            ;;
        --restart|restart)
            if [ -n "$2" ]; then
                restart_service $2
            else
                log_error "请指定要重启的服务名称"
                exit 1
            fi
            ;;
        --status|status)
            log_info "服务状态概览:"
            docker-compose ps
            echo ""
            check_system_resources
            ;;
        --logs|logs)
            if [ -n "$2" ]; then
                view_logs $2 ${3:-50}
            else
                log_error "请指定要查看日志的服务名称"
                exit 1
            fi
            ;;
        --repair|repair)
            auto_repair
            ;;
        --report|report)
            generate_report
            ;;
        --help|help)
            echo "使用方法: $0 [选项] [参数]"
            echo "选项:"
            echo "  --check, check     执行全面健康检查"
            echo "  --restart, restart 重启指定服务"
            echo "  --status, status   显示服务状态"
            echo "  --logs, logs       查看服务日志"
            echo "  --repair, repair   自动修复异常服务"
            echo "  --report, report   生成监控报告"
            echo "  --help, help       显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 check                    # 健康检查"
            echo "  $0 restart backend-api      # 重启后端服务"
            echo "  $0 logs nginx 100           # 查看nginx最近100行日志"
            ;;
        *)
            log_error "未知选项: ${ACTION}"
            log_info "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
}

# 检查依赖
if ! command -v docker-compose &> /dev/null; then
    log_error "docker-compose 命令未找到"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    log_warning "bc 命令未找到，部分检查可能不准确"
fi

# 执行主函数
main "$@"

log_success "监控脚本执行完成！"
