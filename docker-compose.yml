version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ddlpicker-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-ddlpicker123}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ddlpicker}
      MYSQL_USER: ${MYSQL_USER:-ddlpicker}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-ddlpicker123}
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./deployment/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - ddlpicker-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ddlpicker-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./deployment/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ddlpicker-network

  # 后端API服务
  backend-api:
    build:
      context: ./backend-api
      dockerfile: Dockerfile
    container_name: ddlpicker-backend-api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: ${MYSQL_USER:-ddlpicker}
      DB_PASSWORD: ${MYSQL_PASSWORD:-ddlpicker123}
      DB_DATABASE: ${MYSQL_DATABASE:-ddlpicker}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-ddlpicker-jwt-secret-key-production}
      WECHAT_APP_ID: ${WECHAT_APP_ID}
      WECHAT_APP_SECRET: ${WECHAT_APP_SECRET}
      WECHAT_MCH_ID: ${WECHAT_MCH_ID}
      WECHAT_API_KEY: ${WECHAT_API_KEY}
      OSS_ACCESS_KEY_ID: ${OSS_ACCESS_KEY_ID}
      OSS_ACCESS_KEY_SECRET: ${OSS_ACCESS_KEY_SECRET}
      OSS_BUCKET: ${OSS_BUCKET}
      OSS_ENDPOINT: ${OSS_ENDPOINT}
    ports:
      - "3000:3000"
    volumes:
      - ./backend-api/uploads:/app/uploads
      - ./backend-api/logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - ddlpicker-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 管理端Web
  admin-web:
    build:
      context: ./admin-web
      dockerfile: Dockerfile
    container_name: ddlpicker-admin-web
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./deployment/nginx/admin.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend-api
    networks:
      - ddlpicker-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ddlpicker-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/conf.d:/etc/nginx/conf.d
      - ./deployment/ssl:/etc/nginx/ssl
      - ./deployment/nginx/logs:/var/log/nginx
    depends_on:
      - backend-api
      - admin-web
    networks:
      - ddlpicker-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  ddlpicker-network:
    driver: bridge
