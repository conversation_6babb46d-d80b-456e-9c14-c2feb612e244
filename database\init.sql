-- 刀刀乐捡漏网数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS ddlpicker DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ddlpicker;

-- 用户主表
CREATE TABLE users (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL COMMENT '微信openid',
    unionid VARCHAR(64) DEFAULT NULL COMMENT '微信unionid',
    nickname VARCHAR(64) DEFAULT NULL COMMENT '微信昵称',
    avatar_url VARCHAR(255) DEFAULT NULL COMMENT '头像地址',
    phone VARCHAR(20) DEFAULT NULL COMMENT '绑定手机号',
    user_type TINYINT(1) DEFAULT 0 COMMENT '用户类型：0-C端 1-B端 2-内部员工',
    business_license VARCHAR(255) DEFAULT NULL COMMENT '营业执照OSS地址',
    license_status TINYINT(1) DEFAULT 0 COMMENT '执照审核状态：0-待审 1-通过 2-驳回',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_openid (openid),
    KEY idx_user_type (user_type),
    KEY idx_license_status (license_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户主表';

-- 角色表
CREATE TABLE roles (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    name VARCHAR(64) NOT NULL COMMENT '角色名称',
    description VARCHAR(255) DEFAULT NULL COMMENT '角色描述',
    permissions JSON DEFAULT NULL COMMENT '权限列表',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    role_id BIGINT(20) NOT NULL COMMENT '角色ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_role (user_id, role_id),
    KEY idx_user_id (user_id),
    KEY idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 用户地址表
CREATE TABLE user_addresses (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '地址ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    name VARCHAR(64) NOT NULL COMMENT '收货人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '收货人电话',
    province VARCHAR(32) NOT NULL COMMENT '省份',
    city VARCHAR(32) NOT NULL COMMENT '城市',
    district VARCHAR(32) NOT NULL COMMENT '区县',
    detail VARCHAR(255) NOT NULL COMMENT '详细地址',
    is_default TINYINT(1) DEFAULT 0 COMMENT '是否默认地址',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_is_default (is_default)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户地址表';

-- 商品分类表
CREATE TABLE category (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
    parent_id BIGINT(20) DEFAULT 0 COMMENT '父分类ID',
    name VARCHAR(64) NOT NULL COMMENT '分类名称',
    sort_order INT(11) DEFAULT 0 COMMENT '排序',
    icon_url VARCHAR(255) DEFAULT NULL COMMENT '图标地址',
    is_show TINYINT(1) DEFAULT 1 COMMENT '是否显示',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_parent_id (parent_id),
    KEY idx_sort_order (sort_order),
    KEY idx_is_show (is_show)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 品牌表
CREATE TABLE brand (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
    name VARCHAR(64) NOT NULL COMMENT '品牌名称',
    logo_url VARCHAR(255) DEFAULT NULL COMMENT 'LOGO地址',
    sort_order INT(11) DEFAULT 0 COMMENT '排序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='品牌表';

-- SPU商品表
CREATE TABLE spu (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'SPU ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    subtitle VARCHAR(255) DEFAULT NULL COMMENT '副标题',
    category_id BIGINT(20) NOT NULL COMMENT '分类ID',
    brand_id BIGINT(20) DEFAULT NULL COMMENT '品牌ID',
    main_pic_url VARCHAR(255) DEFAULT NULL COMMENT '主图地址',
    video_url VARCHAR(255) DEFAULT NULL COMMENT '视频地址',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：0-下架 1-上架 2-预售',
    freight_template_id BIGINT(20) DEFAULT NULL COMMENT '运费模板ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_category_id (category_id),
    KEY idx_brand_id (brand_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SPU商品表';

-- SKU表
CREATE TABLE sku (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'SKU ID',
    spu_id BIGINT(20) NOT NULL COMMENT 'SPU ID',
    spec_json VARCHAR(500) DEFAULT NULL COMMENT '规格JSON',
    price_c DECIMAL(12,2) NOT NULL COMMENT 'C端价格',
    price_b DECIMAL(12,2) NOT NULL COMMENT 'B端价格',
    stock INT(11) DEFAULT 0 COMMENT '库存',
    weight DECIMAL(8,2) DEFAULT 0.00 COMMENT '重量(kg)',
    bar_code VARCHAR(64) DEFAULT NULL COMMENT '条形码',
    pic_url VARCHAR(255) DEFAULT NULL COMMENT 'SKU图片',
    sales INT(11) DEFAULT 0 COMMENT '销量',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_spu_id (spu_id),
    KEY idx_stock (stock)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SKU表';

-- 购物车表
CREATE TABLE cart (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    sku_id BIGINT(20) NOT NULL COMMENT 'SKU ID',
    quantity INT(11) NOT NULL COMMENT '数量',
    checked TINYINT(1) DEFAULT 1 COMMENT '是否选中',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_sku (user_id, sku_id),
    KEY idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- 订单主表
CREATE TABLE order_master (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    order_type TINYINT(1) DEFAULT 0 COMMENT '订单类型：0-零售 1-批发',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '应付金额',
    pay_amount DECIMAL(12,2) NOT NULL COMMENT '实付金额',
    freight_amount DECIMAL(8,2) DEFAULT 0.00 COMMENT '运费',
    status TINYINT(1) DEFAULT 0 COMMENT '订单状态：0-待付款 1-待发货 2-已发货 3-已收货 4-已完成 5-已取消',
    pay_time DATETIME DEFAULT NULL COMMENT '支付时间',
    delivery_time DATETIME DEFAULT NULL COMMENT '发货时间',
    finish_time DATETIME DEFAULT NULL COMMENT '完成时间',
    close_time DATETIME DEFAULT NULL COMMENT '关闭时间',
    receiver_name VARCHAR(64) NOT NULL COMMENT '收货人姓名',
    receiver_phone VARCHAR(20) NOT NULL COMMENT '收货人电话',
    receiver_addr VARCHAR(255) NOT NULL COMMENT '收货地址',
    remark VARCHAR(255) DEFAULT NULL COMMENT '订单备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_user_id_status (user_id, status),
    KEY idx_created_at (created_at),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单主表'
PARTITION BY RANGE (TO_DAYS(created_at)) (
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p202404 VALUES LESS THAN (TO_DAYS('2024-05-01')),
    PARTITION p202405 VALUES LESS THAN (TO_DAYS('2024-06-01')),
    PARTITION p202406 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p202407 VALUES LESS THAN (TO_DAYS('2024-08-01')),
    PARTITION p202408 VALUES LESS THAN (TO_DAYS('2024-09-01')),
    PARTITION p202409 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p202410 VALUES LESS THAN (TO_DAYS('2024-11-01')),
    PARTITION p202411 VALUES LESS THAN (TO_DAYS('2024-12-01')),
    PARTITION p202412 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 订单明细表
CREATE TABLE order_item (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    order_id BIGINT(20) NOT NULL COMMENT '订单ID',
    sku_id BIGINT(20) NOT NULL COMMENT 'SKU ID',
    sku_name VARCHAR(255) NOT NULL COMMENT 'SKU名称',
    sku_pic VARCHAR(255) DEFAULT NULL COMMENT 'SKU图片',
    spec_json VARCHAR(500) DEFAULT NULL COMMENT '规格JSON',
    price DECIMAL(12,2) NOT NULL COMMENT '单价',
    quantity INT(11) NOT NULL COMMENT '数量',
    total_price DECIMAL(12,2) NOT NULL COMMENT '小计',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_sku_id (sku_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单明细表';

-- 支付流水表
CREATE TABLE payment_log (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '流水ID',
    order_id BIGINT(20) NOT NULL COMMENT '订单ID',
    pay_method TINYINT(1) DEFAULT 0 COMMENT '支付方式：0-微信支付',
    transaction_id VARCHAR(64) DEFAULT NULL COMMENT '微信交易号',
    amount DECIMAL(12,2) NOT NULL COMMENT '支付金额',
    status TINYINT(1) DEFAULT 0 COMMENT '支付状态：0-创建 1-成功 2-失败',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_transaction_id (transaction_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付流水表';

-- 售后退款表
CREATE TABLE refund_order (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '售后ID',
    order_id BIGINT(20) NOT NULL COMMENT '订单ID',
    type TINYINT(1) DEFAULT 0 COMMENT '售后类型：0-仅退款 1-退货退款',
    reason VARCHAR(255) DEFAULT NULL COMMENT '退款原因',
    amount DECIMAL(12,2) NOT NULL COMMENT '退款金额',
    status TINYINT(1) DEFAULT 0 COMMENT '状态：0-申请 1-同意 2-拒绝 3-退款中 4-完成',
    images JSON DEFAULT NULL COMMENT '凭证图片',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='售后退款表';

-- 优惠券模板表
CREATE TABLE coupon_template (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    title VARCHAR(128) NOT NULL COMMENT '优惠券标题',
    type TINYINT(1) DEFAULT 0 COMMENT '类型：0-立减 1-折扣',
    value DECIMAL(8,2) NOT NULL COMMENT '面值/折扣',
    min_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT '最低消费金额',
    total_count INT(11) NOT NULL COMMENT '发放总数',
    received_count INT(11) DEFAULT 0 COMMENT '已领取数量',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_enabled (enabled),
    KEY idx_start_end_time (start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券模板表';

-- 用户优惠券表
CREATE TABLE user_coupon (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
    user_id BIGINT(20) NOT NULL COMMENT '用户ID',
    template_id BIGINT(20) NOT NULL COMMENT '模板ID',
    status TINYINT(1) DEFAULT 0 COMMENT '状态：0-未使用 1-已使用 2-已过期',
    used_order_id BIGINT(20) DEFAULT NULL COMMENT '使用的订单ID',
    received_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    used_at DATETIME DEFAULT NULL COMMENT '使用时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_template_id (template_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券表';

-- 运费模板表
CREATE TABLE freight_template (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
    name VARCHAR(64) NOT NULL COMMENT '模板名称',
    pricing_type TINYINT(1) DEFAULT 0 COMMENT '计费方式：0-按件数 1-按重量',
    default_fee DECIMAL(8,2) NOT NULL COMMENT '默认运费',
    default_num DECIMAL(8,2) NOT NULL COMMENT '默认件数/重量',
    continue_fee DECIMAL(8,2) NOT NULL COMMENT '续费',
    continue_num DECIMAL(8,2) NOT NULL COMMENT '续件数/重量',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运费模板表';

-- 运费区域表
CREATE TABLE freight_area (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '区域ID',
    template_id BIGINT(20) NOT NULL COMMENT '模板ID',
    area_code_list JSON NOT NULL COMMENT '地区编码列表',
    first_fee DECIMAL(8,2) NOT NULL COMMENT '首费',
    first_num DECIMAL(8,2) NOT NULL COMMENT '首件数/重量',
    continue_fee DECIMAL(8,2) NOT NULL COMMENT '续费',
    continue_num DECIMAL(8,2) NOT NULL COMMENT '续件数/重量',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_template_id (template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运费区域表';

-- 系统配置表
CREATE TABLE system_config (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(64) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 管理员用户表
CREATE TABLE admin_user (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    username VARCHAR(64) NOT NULL COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role VARCHAR(32) DEFAULT 'admin' COMMENT '角色',
    last_login_at DATETIME DEFAULT NULL COMMENT '最后登录时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- 审计日志表
CREATE TABLE audit_log (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    action VARCHAR(64) NOT NULL COMMENT '操作动作',
    target_type VARCHAR(32) NOT NULL COMMENT '目标类型',
    target_id BIGINT(20) DEFAULT NULL COMMENT '目标ID',
    detail_json JSON DEFAULT NULL COMMENT '详情JSON',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_operator (operator),
    KEY idx_target (target_type, target_id),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 插入初始数据
INSERT INTO system_config (config_key, config_value, remark) VALUES
('site_name', '刀刀乐捡漏网', '网站名称'),
('site_logo', '', '网站LOGO'),
('wechat_appid', '', '微信小程序AppID'),
('wechat_secret', '', '微信小程序Secret'),
('wechat_mchid', '', '微信商户号'),
('wechat_key', '', '微信支付密钥'),
('oss_endpoint', '', 'OSS访问域名'),
('oss_bucket', '', 'OSS存储桶'),
('sms_access_key', '', '短信AccessKey'),
('sms_secret_key', '', '短信SecretKey');

-- 插入默认管理员账号 (密码: admin123)
INSERT INTO admin_user (username, password_hash, role) VALUES
('admin', '$2b$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXANBjH/G5uyuFAUgdqBzU1b9i', 'super_admin');

-- 插入默认角色
INSERT INTO roles (name, description, permissions) VALUES
('super_admin', '超级管理员', '["*"]'),
('admin', '管理员', '["user:read", "order:read", "product:read", "product:write"]'),
('operator', '运营人员', '["user:read", "order:read", "product:read"]');

-- 插入默认分类
INSERT INTO category (parent_id, name, sort_order, is_show) VALUES
(0, '数码电器', 1, 1),
(0, '服装鞋帽', 2, 1),
(0, '家居用品', 3, 1),
(0, '食品饮料', 4, 1),
(1, '手机通讯', 1, 1),
(1, '电脑办公', 2, 1),
(2, '男装', 1, 1),
(2, '女装', 2, 1);

-- 插入运费模板
INSERT INTO freight_template (name, pricing_type, default_fee, default_num, continue_fee, continue_num) VALUES
('默认运费', 0, 10.00, 1, 5.00, 1),
('包邮模板', 0, 0.00, 1, 0.00, 1);
