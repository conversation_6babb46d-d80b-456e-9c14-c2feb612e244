# 刀刀乐捡漏网开发指南

## 项目概述

刀刀乐捡漏网是一个基于微信小程序的B2B2C电商平台，支持C端用户和B端批发商在同一平台进行购物。

### 技术栈

**前端:**
- 微信小程序 + 微信云开发
- Vue3 + ElementPlus + Vite + Pinia (管理后台)

**后端:**
- Node.js + Koa2 + JWT
- MySQL 8.0 + Redis 6
- 微信云开发 (云函数、云数据库、云存储)

**部署:**
- Docker + Docker Compose
- Nginx
- 腾讯云 Lighthouse

## 开发环境搭建

### 环境要求

- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0
- 微信开发者工具

### 1. 克隆项目

```bash
git clone https://github.com/your-username/DaoDLE.git
cd DaoDLE
```

### 2. 安装依赖

```bash
# 安装根目录依赖
npm install

# 安装所有子项目依赖
npm run install:all
```

### 3. 数据库设置

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE ddlpicker DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入初始数据
mysql -u root -p ddlpicker < database/init.sql
```

### 4. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑后端API配置
cd backend-api
cp .env.example .env
# 编辑 .env 文件，填入数据库和Redis配置

# 编辑管理端配置
cd ../admin-web
cp .env.example .env
# 编辑 .env 文件，填入API地址等配置
```

### 5. 启动开发服务

```bash
# 启动所有服务
npm run dev

# 或分别启动
npm run dev:api    # 后端API (http://localhost:3000)
npm run dev:web    # 管理端Web (http://localhost:8080)
```

### 6. 微信小程序开发

1. 使用微信开发者工具打开 `miniprogram` 目录
2. 配置小程序AppID
3. 开启云开发环境
4. 上传云函数

## 项目结构详解

```
DaoDLE/
├── miniprogram/              # 微信小程序端
│   ├── pages/               # 页面
│   │   ├── index/           # 首页
│   │   ├── category/        # 分类页
│   │   ├── product-detail/  # 商品详情
│   │   ├── cart/            # 购物车
│   │   ├── order-confirm/   # 订单确认
│   │   ├── order-list/      # 订单列表
│   │   ├── user-center/     # 用户中心
│   │   └── upgrade-b/       # B端升级
│   ├── components/          # 组件
│   ├── cloudfunctions/      # 云函数
│   │   ├── getOpenId/       # 获取用户OpenID
│   │   ├── orderCreate/     # 创建订单
│   │   ├── payCallback/     # 支付回调
│   │   └── uploadLicense/   # 上传营业执照
│   ├── utils/               # 工具类
│   ├── app.js               # 应用入口
│   ├── app.json             # 应用配置
│   └── app.wxss             # 全局样式
├── admin-web/               # 管理端Web
│   ├── src/
│   │   ├── views/           # 页面视图
│   │   │   ├── dashboard/   # 仪表盘
│   │   │   ├── product/     # 商品管理
│   │   │   ├── order/       # 订单管理
│   │   │   ├── user/        # 用户管理
│   │   │   └── system/      # 系统管理
│   │   ├── components/      # 组件
│   │   ├── stores/          # Pinia状态管理
│   │   ├── router/          # 路由配置
│   │   ├── api/             # API接口
│   │   ├── utils/           # 工具函数
│   │   └── styles/          # 样式文件
│   ├── public/              # 静态资源
│   └── dist/                # 构建产物
├── backend-api/             # 后端API服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由定义
│   │   ├── middleware/      # 中间件
│   │   ├── services/        # 业务逻辑
│   │   ├── utils/           # 工具函数
│   │   ├── config/          # 配置文件
│   │   └── app.js           # 应用入口
│   ├── tests/               # 测试文件
│   └── logs/                # 日志文件
├── database/                # 数据库脚本
│   ├── init.sql             # 初始化脚本
│   ├── migrations/          # 迁移脚本
│   └── seeds/               # 种子数据
├── deployment/              # 部署配置
│   ├── docker/              # Docker配置
│   ├── nginx/               # Nginx配置
│   └── scripts/             # 部署脚本
└── docs/                    # 文档
```

## 开发规范

### 代码规范

1. **JavaScript/Vue**
   - 使用ESLint + Prettier
   - 遵循Vue3 Composition API规范
   - 组件名使用PascalCase
   - 文件名使用kebab-case

2. **小程序**
   - 页面文件使用kebab-case
   - 组件使用PascalCase
   - 遵循微信小程序开发规范

3. **数据库**
   - 表名使用单数形式
   - 字段名使用snake_case
   - 必须添加注释

### Git规范

```bash
# 提交信息格式
<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(user): 添加用户注册功能
fix(order): 修复订单计算错误
docs(api): 更新API文档
```

### 分支管理

```
main          # 主分支，用于生产环境
develop       # 开发分支
feature/*     # 功能分支
hotfix/*      # 热修复分支
release/*     # 发布分支
```

## API开发指南

### 接口规范

**请求格式:**
```
GET /api/v1/users?page=1&pageSize=20
POST /api/v1/users
PUT /api/v1/users/123
DELETE /api/v1/users/123
```

**响应格式:**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "code": 200,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**错误响应:**
```json
{
  "success": false,
  "message": "参数验证失败",
  "code": 400,
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ]
}
```

### 添加新接口

1. **定义路由** (`backend-api/src/routes/`)
```javascript
// routes/user.js
const Router = require('koa-router')
const { auth, validate } = require('../middleware')
const userController = require('../controllers/user')
const userSchema = require('../schemas/user')

const router = new Router({ prefix: '/api/v1/users' })

router.get('/', auth(), userController.list)
router.post('/', auth(), validate(userSchema.create), userController.create)

module.exports = router
```

2. **实现控制器** (`backend-api/src/controllers/`)
```javascript
// controllers/user.js
const { User } = require('../models')

class UserController {
  async list(ctx) {
    const { page, pageSize } = ctx.state.pagination
    
    const users = await User.findAndCountAll({
      offset: (page - 1) * pageSize,
      limit: pageSize
    })
    
    ctx.body = {
      success: true,
      data: {
        list: users.rows,
        total: users.count,
        page,
        pageSize
      }
    }
  }
  
  async create(ctx) {
    const userData = ctx.request.body
    const user = await User.create(userData)
    
    ctx.body = {
      success: true,
      data: user,
      message: '用户创建成功'
    }
  }
}

module.exports = new UserController()
```

3. **添加验证规则** (`backend-api/src/schemas/`)
```javascript
// schemas/user.js
const Joi = require('joi')

module.exports = {
  create: Joi.object({
    nickname: Joi.string().required(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).required(),
    user_type: Joi.number().valid(0, 1, 2).default(0)
  })
}
```

## 前端开发指南

### Vue3组件开发

```vue
<template>
  <div class="user-list">
    <el-table :data="userList" v-loading="loading">
      <el-table-column prop="nickname" label="昵称" />
      <el-table-column prop="phone" label="手机号" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button @click="editUser(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getUserList } from '@/api/user'

const userList = ref([])
const loading = ref(false)

const loadUsers = async () => {
  loading.value = true
  try {
    const { data } = await getUserList()
    userList.value = data.list
  } catch (error) {
    console.error('加载用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

const editUser = (user) => {
  // 编辑用户逻辑
}

onMounted(() => {
  loadUsers()
})
</script>
```

### 状态管理

```javascript
// stores/user.js
import { defineStore } from 'pinia'
import { login, getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token'),
    userInfo: null,
    permissions: []
  }),
  
  getters: {
    isLogin: (state) => !!state.token,
    hasPermission: (state) => (permission) => {
      return state.permissions.includes(permission)
    }
  },
  
  actions: {
    async login(credentials) {
      const { data } = await login(credentials)
      this.token = data.token
      localStorage.setItem('token', data.token)
      await this.getUserInfo()
    },
    
    async getUserInfo() {
      const { data } = await getUserInfo()
      this.userInfo = data.user
      this.permissions = data.permissions
    },
    
    logout() {
      this.token = null
      this.userInfo = null
      this.permissions = []
      localStorage.removeItem('token')
    }
  }
})
```

## 测试指南

### 单元测试

```javascript
// tests/user.test.js
const request = require('supertest')
const app = require('../src/app')

describe('User API', () => {
  test('GET /api/v1/users should return user list', async () => {
    const response = await request(app)
      .get('/api/v1/users')
      .set('Authorization', 'Bearer ' + token)
      .expect(200)
    
    expect(response.body.success).toBe(true)
    expect(Array.isArray(response.body.data.list)).toBe(true)
  })
  
  test('POST /api/v1/users should create user', async () => {
    const userData = {
      nickname: '测试用户',
      phone: '13800138000'
    }
    
    const response = await request(app)
      .post('/api/v1/users')
      .set('Authorization', 'Bearer ' + token)
      .send(userData)
      .expect(200)
    
    expect(response.body.success).toBe(true)
    expect(response.body.data.nickname).toBe(userData.nickname)
  })
})
```

### 运行测试

```bash
# 后端测试
cd backend-api
npm test

# 前端测试
cd admin-web
npm run test

# 覆盖率测试
npm run test:coverage
```

## 调试指南

### 后端调试

```bash
# 开发模式启动（自动重启）
npm run dev

# 调试模式
npm run debug

# 查看日志
tail -f backend-api/logs/app.log
```

### 前端调试

```bash
# 开发模式
npm run dev

# 构建分析
npm run build:analyze

# 类型检查
npm run type-check
```

### 小程序调试

1. 使用微信开发者工具的调试功能
2. 查看云函数日志
3. 使用vconsole进行移动端调试

## 常见问题

### 1. 数据库连接失败
检查数据库配置和服务状态

### 2. Redis连接失败
确认Redis服务已启动

### 3. 微信支付回调失败
检查回调URL配置和网络连通性

### 4. 图片上传失败
检查OSS配置和权限设置

### 5. 小程序云函数部署失败
检查云开发环境配置和权限

## 性能优化建议

1. **数据库优化**
   - 合理使用索引
   - 避免N+1查询
   - 使用连接池

2. **缓存策略**
   - Redis缓存热点数据
   - 浏览器缓存静态资源
   - CDN加速

3. **前端优化**
   - 代码分割
   - 懒加载
   - 图片压缩

4. **接口优化**
   - 分页查询
   - 字段筛选
   - 响应压缩

## 联系方式

如有开发相关问题，请联系：
- 技术负责人：<EMAIL>
- 项目经理：<EMAIL>
- 技术群：微信群二维码
