[mysqld]
# 基础配置
default-authentication-plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 时区设置
default-time-zone='+8:00'

# 连接配置
max_connections=1000
max_connect_errors=100000
max_allowed_packet=64M
interactive_timeout=28800
wait_timeout=28800

# 缓存配置
query_cache_type=1
query_cache_size=128M
query_cache_limit=4M

# InnoDB配置
innodb_buffer_pool_size=512M
innodb_log_file_size=256M
innodb_log_buffer_size=16M
innodb_flush_log_at_trx_commit=2
innodb_file_per_table=1
innodb_open_files=400

# 慢查询日志
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 错误日志
log-error=/var/log/mysql/error.log

# 二进制日志
log-bin=/var/log/mysql/mysql-bin
binlog_format=ROW
expire_logs_days=7

# 安全配置
skip-name-resolve
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
