# 后端API环境配置示例

# 应用环境
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=ddlpicker
DB_PASSWORD=ddlpicker123
DB_DATABASE=ddlpicker

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=ddlpicker-jwt-secret-key-development
JWT_EXPIRES_IN=2h
JWT_REFRESH_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=
WECHAT_APP_SECRET=
WECHAT_MCH_ID=
WECHAT_API_KEY=
WECHAT_NOTIFY_URL=http://localhost:3000/api/v1/payment/wechat/notify

# 阿里云OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=
OSS_ACCESS_KEY_SECRET=
OSS_BUCKET=
OSS_ENDPOINT=

# 短信配置
SMS_ACCESS_KEY_ID=
SMS_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=刀刀乐捡漏网
SMS_TEMPLATE_CODE=

# CORS配置
CORS_ORIGIN=http://localhost:8080

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_PATH=./uploads

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=ddlpicker:dev:
