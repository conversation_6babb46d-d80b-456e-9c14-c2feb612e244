# 刀刀乐捡漏网快速开始指南

## 项目简介

刀刀乐捡漏网是一个基于微信小程序的B2B2C电商平台，支持C端用户和B端批发商在同一平台进行购物。

## 快速体验

### 在线演示
- **管理后台**: https://admin.ddlpicker.com
- **API文档**: https://api.ddlpicker.com/docs
- **小程序码**: [扫描二维码体验]

### 演示账号
- **管理员账号**: admin / admin123
- **普通用户**: 使用微信扫码登录小程序

## 本地开发

### 环境准备

1. **安装Node.js**
   ```bash
   # 下载并安装Node.js 16+
   https://nodejs.org/
   ```

2. **安装数据库**
   ```bash
   # MySQL 8.0
   https://dev.mysql.com/downloads/mysql/
   
   # Redis 6.0
   https://redis.io/download
   ```

3. **安装微信开发者工具**
   ```bash
   https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
   ```

### 快速启动

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/DaoDLE.git
   cd DaoDLE
   ```

2. **安装依赖**
   ```bash
   npm run install:all
   ```

3. **配置环境**
   ```bash
   # 复制环境配置
   cp .env.example .env
   cp backend-api/.env.example backend-api/.env
   cp admin-web/.env.example admin-web/.env
   
   # 编辑配置文件，填入数据库等信息
   ```

4. **初始化数据库**
   ```bash
   # 创建数据库
   mysql -u root -p
   CREATE DATABASE ddlpicker;
   
   # 导入初始数据
   mysql -u root -p ddlpicker < database/init.sql
   ```

5. **启动服务**
   ```bash
   # 启动后端API
   cd backend-api
   npm run dev
   
   # 新开终端，启动管理端
   cd admin-web
   npm run dev
   ```

6. **访问应用**
   - 后端API: http://localhost:3000
   - 管理后台: http://localhost:8080
   - 小程序: 使用微信开发者工具打开miniprogram目录

## Docker快速部署

### 开发环境

```bash
# 启动数据库和Redis
docker-compose up -d mysql redis

# 等待数据库启动
sleep 30

# 手动启动后端和前端
cd backend-api && npm run dev &
cd admin-web && npm run dev &
```

### 生产环境

```bash
# 配置环境变量
cp .env.example .env
# 编辑.env文件

# 一键部署
./deployment/scripts/deploy.sh production

# 或使用Docker Compose
docker-compose up -d
```

## 功能特性

### 🛒 电商核心功能
- ✅ 商品管理（SPU/SKU）
- ✅ 购物车
- ✅ 订单管理
- ✅ 支付集成（微信支付）
- ✅ 优惠券系统
- ✅ 库存管理

### 👥 用户系统
- ✅ 微信登录
- ✅ C端/B端用户区分
- ✅ 营业执照审核
- ✅ 差异化价格
- ✅ 用户权限管理

### 📊 管理后台
- ✅ 数据统计仪表盘
- ✅ 用户管理
- ✅ 商品管理
- ✅ 订单管理
- ✅ 营销管理
- ✅ 系统配置

### 📱 微信小程序
- ✅ 商品浏览
- ✅ 搜索功能
- ✅ 购物车
- ✅ 订单流程
- ✅ 用户中心
- ✅ B端升级

## 技术架构

### 前端技术
- **小程序**: 微信小程序原生开发
- **管理端**: Vue3 + ElementPlus + Vite + Pinia
- **样式**: SCSS + 响应式设计

### 后端技术
- **框架**: Node.js + Koa2
- **数据库**: MySQL 8.0 + Redis 6
- **认证**: JWT + 权限控制
- **文件存储**: 阿里云OSS

### 部署技术
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **HTTPS**: SSL证书
- **监控**: 日志 + 健康检查

## 开发指南

### 代码结构
```
DaoDLE/
├── miniprogram/     # 微信小程序
├── admin-web/       # 管理端Web
├── backend-api/     # 后端API
├── database/        # 数据库脚本
├── deployment/      # 部署配置
└── docs/           # 文档
```

### 开发规范
- **代码风格**: ESLint + Prettier
- **提交规范**: Conventional Commits
- **分支管理**: Git Flow
- **测试**: Jest + 单元测试

### API接口
- **RESTful**: 标准REST API设计
- **认证**: JWT Bearer Token
- **文档**: Swagger自动生成
- **限流**: 接口频率限制

## 部署指南

### 服务器要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上SSD
- **系统**: CentOS 8 / Ubuntu 20.04

### 域名配置
```
主站: ddlpicker.com
管理后台: admin.ddlpicker.com
API接口: api.ddlpicker.com
```

### SSL证书
```bash
# 使用Let's Encrypt免费证书
certbot certonly --standalone -d ddlpicker.com
```

### 监控运维
```bash
# 服务状态检查
./deployment/scripts/monitor.sh check

# 数据备份
./deployment/scripts/backup.sh all

# 服务重启
./deployment/scripts/start.sh production
```

## 常见问题

### Q: 如何配置微信小程序？
A: 在微信公众平台注册小程序，获取AppID和AppSecret，配置到环境变量中。

### Q: 如何配置支付功能？
A: 需要开通微信商户号，获取商户ID和API密钥，配置支付回调地址。

### Q: 如何上传商品图片？
A: 配置阿里云OSS，获取AccessKey，在管理后台可直接上传图片。

### Q: 如何备份数据？
A: 使用提供的备份脚本，支持数据库和文件的自动备份。

### Q: 如何扩展功能？
A: 参考开发文档，按照现有架构添加新的API接口和页面。

## 技术支持

### 文档资源
- [开发指南](./development.md)
- [部署指南](./deployment.md)
- [API文档](./api.md)

### 联系方式
- **技术支持**: <EMAIL>
- **问题反馈**: https://github.com/your-username/DaoDLE/issues
- **技术交流群**: [微信群二维码]

### 开源协议
本项目采用MIT开源协议，欢迎贡献代码和提出建议。

---

**立即开始体验刀刀乐捡漏网，打造您的专属B2B2C电商平台！**
