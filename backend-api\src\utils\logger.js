const winston = require('winston')
const path = require('path')
const config = require('../config')

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`
    
    if (stack) {
      log += `\n${stack}`
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`
    }
    
    return log
  })
)

// 创建日志器
const logger = winston.createLogger({
  level: config.log.level,
  format: logFormat,
  defaultMeta: {
    service: 'ddlpicker-backend-api'
  },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(path.dirname(config.log.file), 'error.log'),
      level: 'error',
      maxsize: config.log.maxSize,
      maxFiles: config.log.maxFiles
    }),
    
    // 所有日志文件
    new winston.transports.File({
      filename: config.log.file,
      maxsize: config.log.maxSize,
      maxFiles: config.log.maxFiles
    })
  ]
})

// 开发环境下同时输出到控制台
if (config.env !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }))
}

// 生产环境下的异常处理
if (config.env === 'production') {
  logger.exceptions.handle(
    new winston.transports.File({
      filename: path.join(path.dirname(config.log.file), 'exceptions.log')
    })
  )
  
  logger.rejections.handle(
    new winston.transports.File({
      filename: path.join(path.dirname(config.log.file), 'rejections.log')
    })
  )
}

module.exports = logger
