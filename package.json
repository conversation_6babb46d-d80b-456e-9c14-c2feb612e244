{"name": "ddlpicker", "version": "1.0.0", "description": "刀刀乐捡漏网 - B2B2C微信小程序电商系统", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:web\"", "dev:api": "cd backend-api && npm run dev", "dev:web": "cd admin-web && npm run dev", "build": "npm run build:api && npm run build:web", "build:api": "cd backend-api && npm run build", "build:web": "cd admin-web && npm run build", "install:all": "npm install && cd backend-api && npm install && cd ../admin-web && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "migrate": "cd backend-api && npm run migrate", "seed": "cd backend-api && npm run seed"}, "keywords": ["wechat", "miniprogram", "ecommerce", "b2b2c", "nodejs", "vue3"], "author": "AI-PM", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}, "workspaces": ["backend-api", "admin-web"]}